-- proposal_undertake_unit definition

CREATE TABLE `proposal_undertake_unit` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `handle_id` bigint DEFAULT NULL COMMENT '办理id',
    `dept_id` bigint DEFAULT NULL COMMENT '承办部门id',
    `undertake_way` varchar(20) DEFAULT NULL COMMENT '承办方式',
    `undertake_time` date DEFAULT NULL COMMENT '承办时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='提案承办单位';


ALTER TABLE `proposal_handle` MODIFY COLUMN organizers VARCHAR(400) NULL COMMENT '承办单位';
ALTER TABLE `proposal_handle` ADD proposal_id BIGINT NULL COMMENT '提案id';
ALTER TABLE `proposal_handle` CHANGE proposal_id proposal_id BIGINT NULL COMMENT '提案id' AFTER id;
ALTER TABLE `proposal_handle` CHANGE organizers organizers varchar(400) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '承办单位' AFTER proposal_id;
ALTER TABLE `proposal_handle` ADD will_send_sms BOOL DEFAULT false NULL COMMENT '是否发送短信';
ALTER TABLE `proposal_handle` CHANGE will_send_sms will_send_sms BOOL DEFAULT false NULL COMMENT '是否发送短信' AFTER feedback_status;



ALTER TABLE `proposal_verify_record` ADD measure varchar(200) NULL COMMENT '处理措施';
ALTER TABLE `proposal_verify_record` CHANGE measure measure varchar(200) NULL COMMENT '处理措施' AFTER verify_log;
ALTER TABLE `proposal_verify_record` CHANGE verifier_id verifier VARCHAR(100) NULL COMMENT '审核人';
ALTER TABLE `proposal_verify_record` MODIFY COLUMN verifier VARCHAR(100) NULL COMMENT '审核人';
ALTER TABLE `proposal_verify_record` CHANGE verifier verifier VARCHAR(100) NULL COMMENT '审核人' AFTER measure;
ALTER TABLE `proposal_verify_record` MODIFY COLUMN verify_time DATETIME NULL COMMENT '审核时间';
ALTER TABLE `proposal_verify_record` ADD verify_type varchar(20) NULL COMMENT '审核类型';
ALTER TABLE `proposal_verify_record` CHANGE verify_type verify_type varchar(20) NULL COMMENT '审核类型' AFTER proposal_id;
ALTER TABLE `proposal_verify_record` ADD verify_reason varchar(200) NULL COMMENT '审核原因（不立案理由）';
ALTER TABLE `proposal_verify_record` CHANGE verify_reason verify_reason varchar(200) NULL COMMENT '审核原因（不立案理由）' AFTER verify_process;
ALTER TABLE `proposal_verify_record` ADD relate_unit varchar(100) NULL COMMENT '相关部门';
ALTER TABLE `proposal_verify_record` CHANGE relate_unit relate_unit varchar(100) NULL COMMENT '相关部门' AFTER verifier;


-- proposal_merge definition
CREATE TABLE `proposal_merge` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `main_proposal` bigint DEFAULT NULL COMMENT '主提案id',
    `merge_proposal` text COMMENT '并案提案id',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='并案信息表';

-- 提案承办单位信息表结构更新
ALTER TABLE `proposal_undertake_unit` CHANGE undertake_time deadline date NULL COMMENT '办理截止日期';
ALTER TABLE `proposal_undertake_unit` MODIFY COLUMN deadline date NULL COMMENT '办理截止日期';
ALTER TABLE `proposal_undertake_unit` ADD undertake_result varchar(5) NULL COMMENT '办理结果';
ALTER TABLE `proposal_undertake_unit` CHANGE undertake_result undertake_result varchar(5) NULL COMMENT '办理结果' AFTER undertake_way;
ALTER TABLE `proposal_undertake_unit` ADD handle_way varchar(20) NULL COMMENT '办理方式';
ALTER TABLE `proposal_undertake_unit` CHANGE handle_way handle_way varchar(20) NULL COMMENT '办理方式' AFTER undertake_result;
ALTER TABLE `proposal_undertake_unit` ADD handle_status BOOL DEFAULT false NULL COMMENT '办理状态';
ALTER TABLE `proposal_undertake_unit` CHANGE handle_status handle_status BOOL DEFAULT false NULL COMMENT '办理状态' AFTER deadline;



-- 2025-03-13 办理答复表结构更新
ALTER TABLE `proposal_feedback` ADD undertake_result varchar(5) NULL COMMENT '办理结果';
ALTER TABLE `proposal_feedback` CHANGE undertake_result undertake_result varchar(100) NULL COMMENT '办理结果' AFTER content;
ALTER TABLE `proposal_feedback` ADD handle_way varchar(20) NULL COMMENT '办理方式';
ALTER TABLE `proposal_feedback` CHANGE handle_way handle_way varchar(20) NULL COMMENT '办理方式' AFTER undertake_result;
ALTER TABLE `proposal_feedback` ADD undertake_person varchar(100) NULL COMMENT '承办人员';
ALTER TABLE `proposal_feedback` CHANGE undertake_person undertake_person varchar(100) NULL COMMENT '承办人员' AFTER handle_way;
ALTER TABLE `proposal_feedback` ADD is_open BOOL DEFAULT false NULL COMMENT '公开类型';
ALTER TABLE `proposal_feedback` CHANGE is_open is_open BOOL DEFAULT false NULL COMMENT '公开类型' AFTER issue_user;
ALTER TABLE `proposal_feedback` ADD contact_phone varchar(20) NULL COMMENT '联系电话';
ALTER TABLE `proposal_feedback` CHANGE contact_phone contact_phone varchar(20) NULL COMMENT '联系电话' AFTER undertake_person;
ALTER TABLE `proposal_feedback` ADD proposal_quality varchar(15) NULL COMMENT '提案质量';
ALTER TABLE `proposal_feedback` CHANGE proposal_quality proposal_quality varchar(15) NULL COMMENT '提案质量' AFTER contact_phone;
ALTER TABLE `proposal_feedback` ADD finish_time DATETIME NULL COMMENT '办结日期';
ALTER TABLE `proposal_feedback` CHANGE finish_time finish_time DATETIME NULL COMMENT '办结日期' AFTER is_open;
ALTER TABLE `proposal_feedback` MODIFY COLUMN issue_user VARCHAR(100) NULL COMMENT '签发人';
ALTER TABLE `proposal_feedback` CHANGE issue_date undertake_way varchar(20) NULL COMMENT '承办方式';
ALTER TABLE `proposal_feedback` MODIFY COLUMN undertake_way varchar(20) NULL COMMENT '承办方式';
ALTER TABLE `proposal_feedback` CHANGE undertake_way undertake_way varchar(20) NULL COMMENT '承办方式' AFTER dept_id;
ALTER TABLE `proposal_feedback` DROP COLUMN title;

ALTER TABLE `proposal_handle` ADD handle_count INTEGER NULL COMMENT '办理次数';
ALTER TABLE `proposal_handle` CHANGE handle_count handle_count INTEGER DEFAULT 0 COMMENT '办理次数' AFTER undertake_time;

-- 2025-03-17
ALTER TABLE `proposal_undertake_unit` CHANGE dept_id unit_id bigint NULL COMMENT '承办部门id';
ALTER TABLE `proposal_reception` CHANGE dept_id recipient_id bigint NULL COMMENT '接收人id';
ALTER TABLE `proposal_reception` MODIFY COLUMN recipient_id bigint NULL COMMENT '接收人id';
ALTER TABLE `proposal_feedback` CHANGE dept_id reply_user_id bigint NULL COMMENT '答复人id';


