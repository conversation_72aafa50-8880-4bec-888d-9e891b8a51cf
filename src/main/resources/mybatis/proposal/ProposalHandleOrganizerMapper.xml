<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalHandleOrganizerMapper">

    <insert id="insertProposalHandleOrganizer" parameterType="com.ruoyi.project.proposal.domain.ProposalHandleOrganizer">
        INSERT INTO proposal_handle_organizer(handle_id, dept_id)
        VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.handleId},#{item.deptId})
            </foreach>
    </insert>

    <select id="selectOrganizerList" resultType="java.util.HashMap">
        SELECT
            pho.handle_id,
            d.dept_name
        FROM proposal_handle_organizer pho
            JOIN sys_dept d ON pho.dept_id = d.dept_id
        WHERE pho.handle_id IN
        <foreach collection="handleIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
