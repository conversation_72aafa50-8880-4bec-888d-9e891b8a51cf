<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalFeedbackMapper">

    <select id="selectProposalFeedbackList" resultType="com.ruoyi.project.proposal.domain.vo.ProposalFeedbackVo">
        SELECT
            pf.id,
            p.case_number,
            p.proposer,
            u.user_name AS dept_name,
            pf.undertake_way,
            pf.undertake_result,
            pf.handle_way,
            pf.is_open,
            pf.proposal_quality,
            pf.content,
            pf.create_time,
            pf.issue_user,
            pf.undertake_person,
            pf.contact_phone
        FROM proposal p
            INNER JOIN proposal_feedback pf ON pf.proposal_id = p.id
            INNER JOIN sys_user u ON u.user_id = pf.reply_user_id
        WHERE p.del_flag = 0
          AND p.id = #{proposalId}
    </select>


    <select id="selectProposalFeedbackAnnexList" resultType="com.ruoyi.project.proposal.domain.vo.AnnexVo">
        SELECT
            a.id,
            a.url,
            a.annex_name AS annexName,
            a.annex_type AS annexType
        FROM proposal_feedback_annex pfa
            LEFT JOIN annex a ON a.id = pfa.annex_id
        WHERE pfa.feedback_id = #{feedbackId}
    </select>

</mapper>