<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalQualityEvaluationMapper">
    
    <resultMap type="ProposalQualityEvaluation" id="ProposalQualityEvaluationResult">
        <result property="id"    column="id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="evaluation"    column="evaluation"    />
        <result property="evaluationTime"    column="evaluation_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProposalQualityEvaluationVo">
        select id, dept_name, evaluation, evaluation_time, del_flag from proposal_quality_evaluation
    </sql>

    <select id="selectProposalQualityEvaluationList" parameterType="ProposalQualityEvaluation" resultMap="ProposalQualityEvaluationResult">
        <include refid="selectProposalQualityEvaluationVo"/>
        <where>  
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="evaluation != null  and evaluation != ''"> and evaluation = #{evaluation}</if>
            <if test="evaluationTime != null "> and evaluation_time = #{evaluationTime}</if>
        </where>
    </select>


    <delete id="deleteProposalQualityEvaluationByIds" parameterType="String">
        delete from proposal_quality_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectEvaluationList" resultType="com.ruoyi.project.proposal.domain.vo.ProposalQualityEvaluationVo">
        SELECT
            d.dept_name,
            qe.evaluation,
            qe.evaluation_time
        FROM proposal_quality_evaluation qe
            LEFT JOIN sys_dept d ON qe.dept_id = d.dept_id
        WHERE qe.proposal_id = #{proposalId}
    </select>
</mapper>