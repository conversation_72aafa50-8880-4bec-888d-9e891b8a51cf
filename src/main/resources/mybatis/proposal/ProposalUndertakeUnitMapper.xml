<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalUndertakeUnitMapper">

    <select id="selectUndertakeUnitByProposalId" resultType="com.ruoyi.project.proposal.domain.ProposalUndertakeUnit">
        SELECT
            pu.*
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
            LEFT JOIN proposal_undertake_unit pu ON pu.handle_id = ph.id
        WHERE p.del_flag = 0
          AND p.id = #{proposalId}
          AND pu.unit_id = #{unitId}
    </select>

    <select id="selectUndertakeUnitList" resultType="com.ruoyi.project.proposal.domain.ProposalUndertakeUnit">
        SELECT
            pu.*
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
            LEFT JOIN proposal_undertake_unit pu ON pu.handle_id = ph.id AND pu.del_flag = 0
        WHERE p.del_flag = 0
          AND p.id = #{proposalId}
    </select>
</mapper>