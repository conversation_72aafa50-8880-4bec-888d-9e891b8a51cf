<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalHandleMapper">
    
    <resultMap id="ProposalHandleEvaPageVo" type="com.ruoyi.project.proposal.domain.vo.ProposalHandleEvaPageVo" >
        <result property="id"    column="id"/>
        <result property="proposalId" column="proposal_id"/>
        <result property="year"    column="year"/>
        <result property="caseNumber"    column="case_number"/>
        <result property="caseReason"    column="case_reason"/>
        <result property="proposer" column="proposer"/>
        <result property="organizers"    column="organizers"/>
        <result property="undertakeResult"    column="undertake_result"/>
        <result property="undertakePerson"    column="undertake_person"/>
        <result property="proposalQuality"    column="proposal_quality"/>
        <result property="undertakeTime"    column="undertake_time"/>
        <result property="evaluationStatus"    column="evaluation_status"/>
        <collection property="organizerList"
                    ofType="java.lang.String"
                    column="id"
                    select="selectOrganizersById"/>
    </resultMap>

    <resultMap id="ProposalHandleRevertPageVo" type="com.ruoyi.project.proposal.domain.vo.ProposalHandlePageVo" >
        <result property="id"    column="id"/>
        <result property="proposalId" column="proposal_id"/>
        <result property="year"    column="year"/>
        <result property="caseNumber"    column="case_number"/>
        <result property="caseReason"    column="case_reason"/>
        <result property="proposer" column="proposer"/>
        <result property="organizers"    column="organizers"/>
        <result property="undertakeResult"    column="undertake_result"/>
        <result property="handleWay" column="handle_way"/>
        <result property="feedbackStatus" column="feedback_status"/>
        <result property="revertStatus" column="revert_status"/>
        <collection property="organizerList"
                    ofType="java.lang.String"
                    column="id"
                    select="selectOrganizersById"/>
    </resultMap>

    <select id="selectProposalHandleEvaPage" resultMap="ProposalHandleEvaPageVo">
        SELECT
            ph.id,
            p.id AS proposal_id,
            p.year AS year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason AS case_reason,
            p.proposer,
            d.dept_name AS organizers,
            u.user_name AS undertake_person,
            ph.undertake_result,
            ph.handle_way,
            ph.proposal_quality,
            ph.undertake_time,
            ph.evaluation_status
        FROM proposal_handle ph
            LEFT JOIN proposal_handle_rel pur ON pur.handle_id = ph.id
            LEFT JOIN proposal p ON pur.proposal_id = p.id
            LEFT JOIN sys_user u ON ph.undertake_person = u.user_id
            LEFT JOIN proposal_handle_organizer pho ON pho.handle_id = ph.id
            LEFT JOIN sys_dept d ON pho.dept_id = d.dept_id
        WHERE p.del_flag = false
        <if test="pageParam.year != null and pageParam.year.length == 2">
            AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
        </if>
        <if test="pageParam.caseNumber != null and pageParam.caseNumber != ''">
            AND p.case_number = #{pageParam.caseNumber}
        </if>
        <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
            AND p.case_reason LIKE  concat('%', #{pageParam.caseReason} ,'%')
        </if>
        <if test="pageParam.proposer != null and pageParam.proposer != ''">
            AND p.proposer LIKE concat('%', #{pageParam.proposer} ,'%')
        </if>
        <if test="pageParam.organizers != null and pageParam.organizers != null">
            AND d.dept_name LIKE concat('%', #{pageParam.organizers} ,'%')
        </if>
        <if test="pageParam.isHandle != null">
            AND p.is_finished = #{pageParam.isHandle}
        </if>
        <if test="pageParam.evaluationStatus != null">
            AND ph.evaluation_status = #{pageParam.evaluationStatus}
        </if>
        <if test="pageParam.undertakePerson != null and pageParam.undertakePerson != ''">
            AND ph.undertake_person LIKE concat('%', #{pageParam.undertakePerson} ,'%')
        </if>
        <if test="pageParam.undertakeTime != null">
            AND ph.undertake_time = #{pageParam.undertakeTime}
        </if>
        <if test="pageParam.handleWay != null">
            AND ph.handle_way = #{pageParam.handleWay}
        </if>
        <if test="pageParam.undertakeResult != null">
            AND ph.undertake_result = #{pageParam.undertakeResult}
        </if>
        <if test="pageParam.proposalQuality != null">
            AND ph.proposal_quality = #{pageParam.proposalQuality}
        </if>
    </select>

    <select id="selectProposalHandleRevertPage" resultMap="ProposalHandleRevertPageVo">
        SELECT
            ph.id,
            p.id AS proposal_id,
            p.year AS year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason AS case_reason,
            p.proposer,
            d.dept_name AS organizers,
            ph.undertake_way AS undertake_way,
            ph.undertake_result,
            ph.handle_way,
            ph.feedback_status,
            ph.revert_status
        FROM proposal_handle ph
            LEFT JOIN proposal_handle_rel pur ON pur.handle_id = ph.id
            LEFT JOIN proposal p ON pur.proposal_id = p.id
            LEFT JOIN sys_user u ON ph.undertake_person = u.user_id
            LEFT JOIN proposal_handle_organizer pho ON pho.handle_id = ph.id
            LEFT JOIN sys_dept d ON pho.dept_id = d.dept_id
        WHERE p.del_flag = false
        <if test="pageParam.year != null and pageParam.year.length == 2">
            AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
        </if>
        <if test="pageParam.caseNumber != null and pageParam.caseNumber != ''">
            AND p.case_number = #{pageParam.caseNumber}
        </if>
        <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
            AND p.case_reason LIKE  concat('%', #{pageParam.caseReason} ,'%')
        </if>
        <if test="pageParam.proposer != null and pageParam.proposer != ''">
            AND p.proposer LIKE concat('%', #{pageParam.proposer} ,'%')
        </if>
        <if test="pageParam.organizers != null and pageParam.organizers != null">
            AND d.dept_name LIKE concat('%', #{pageParam.organizers} ,'%')
        </if>
        <if test="pageParam.undertakePerson != null and pageParam.undertakePerson != ''">
            AND ph.undertake_person LIKE concat('%', #{pageParam.undertakePerson} ,'%')
        </if>
        <if test="pageParam.feedbackStatus != null">
            AND ph.feedback_status = #{pageParam.feedbackStatus}
        </if>
        <if test="pageParam.undertakeResult != null">
            AND ph.undertake_result = #{pageParam.undertakeResult}
        </if>
    </select>

    <select id="selectEvaluationExcelDtoList" resultType="com.ruoyi.project.proposal.domain.dto.EvaluationExcelDto">
        SELECT
            p.id AS proposalId,
            ph.id AS handleId,
            ROW_NUMBER() OVER (ORDER BY p.id) AS no,
            p.year,
            ph.undertake_result,
            u.user_name AS undertakePerson,
            ph.handle_way,
            ph.proposal_quality,
            pe.*
        FROM proposal p
            LEFT JOIN proposal_handle_rel phr ON phr.proposal_id = p.id
            LEFT JOIN proposal_handle ph ON ph.id = phr.handle_id
            LEFT JOIN proposal_handle_evaluations pe ON pe.handle_id = ph.id
            LEFT JOIN sys_user u ON ph.undertake_person = u.user_id
        WHERE p.del_flag = false

    </select>

    <select id="selectUsersByProposalId" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUserVo">
        SELECT
            u.user_id,
            u.user_name,
            pur.submit_type
        FROM proposal_user_rel pur
            JOIN sys_user u ON pur.proposer_id = u.user_id
        WHERE pur.proposal_id = #{proposalId}
    </select>

    <select id="selectOrganizersById" resultType="java.lang.String">
        SELECT
            d.dept_name
        FROM proposal_handle_organizer pho
            JOIN sys_dept d ON pho.dept_id = d.dept_id
        WHERE pho.handle_id = #{id}
    </select>


    <select id="getOrganizerListByProposalId" resultType="java.lang.String">
        SELECT d.dept_name
        FROM proposal_handle_rel phr
            LEFT JOIN proposal_handle_organizer pho ON phr.handle_id = pho.handle_id
            LEFT JOIN sys_dept d ON pho.dept_id = d.dept_id
        WHERE phr.proposal_id = #{proposalId}
    </select>

    <select id="selectProposalHandleBackPage"
            resultType="com.ruoyi.project.proposal.domain.vo.ProposalHandlePageVo">
        SELECT
            p.id AS proposal_id,
            p.year AS year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason AS case_reason,
            p.proposer,
            (SELECT u.user_name FROM sys_user u WHERE u.user_id = pu.unit_id) AS organizers,
            pu.undertake_way,
            pu.undertake_result,
            pu.handle_way,
            pu.handle_status AS feedback_status
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
            LEFT JOIN proposal_undertake_unit pu ON pu.handle_id = ph.id AND pu.del_flag = false
        WHERE p.del_flag = false
            AND pu.undertake_way = 'ASSISTANT_OFFICE'
            AND ph.id IN
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        <if test="pageParam.year != null and pageParam.year.length == 2">
            AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
        </if>
        <if test="pageParam.caseNumber != null">
            AND p.case_number LIKE  CONCAT('%', #{pageParam.caseNumber}, '%')
        </if>
        <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
            AND p.case_reason LIKE  CONCAT('%', #{pageParam.caseReason}, '%')
        </if>
        <if test="pageParam.proposer != null and pageParam.proposer != ''">
            AND p.proposer LIKE CONCAT('%', #{pageParam.proposer}, '%')
        </if>
        <if test="pageParam.organizers != null and pageParam.organizers != ''">
            AND ph.organizers LIKE CONCAT('%', #{pageParam.organizers}, '%')
        </if>
        <if test="pageParam.undertakeResult != null">
            AND pu.undertake_result = #{pageParam.undertakeResult}
        </if>
        <if test="pageParam.feedbackStatus != null">
            AND pu.handle_status = #{pageParam.feedbackStatus}
        </if>
    </select>

    <select id="selectProposalHandlePage"
            resultType="com.ruoyi.project.proposal.domain.vo.ProposalHandlePageVo">
        SELECT
            p.id AS proposal_id,
            p.year AS year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason AS case_reason,
            p.case_type AS case_type,
            pu.undertake_way AS undertake_way,
            p.proposer,
            (SELECT MAX(pr.join_time) FROM proposal_reception pr WHERE pr.proposal_id = p.id AND pr.recipient_id = pu.unit_id AND pr.del_flag = false) AS join_time,
            pu.deadline AS deadline,
            pu.handle_status
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
            LEFT JOIN proposal_undertake_unit pu ON pu.handle_id = ph.id AND pu.del_flag = false
        WHERE p.del_flag = false
          AND pu.unit_id = #{unitId}
        <if test="pageParam.year != null and pageParam.year.length == 2">
            AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
        </if>
        <if test="pageParam.caseNumber != null">
            AND p.case_number LIKE  CONCAT('%', #{pageParam.caseNumber}, '%')
        </if>
        <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
            AND p.case_reason LIKE  CONCAT('%', #{pageParam.caseReason}, '%')
        </if>
        <if test="pageParam.proposer != null and pageParam.proposer != ''">
            AND p.proposer LIKE CONCAT('%', #{pageParam.proposer}, '%')
        </if>
        <if test="pageParam.caseType != null">
            AND p.case_type = #{pageParam.caseType}
        </if>
        <if test="pageParam.isHandle != null">
            AND p.is_finished = #{pageParam.isHandle}
        </if>
        <if test="pageParam.undertakeWay != null">
            AND pu.undertake_way = #{pageParam.undertakeWay}
        </if>

    </select>
</mapper>