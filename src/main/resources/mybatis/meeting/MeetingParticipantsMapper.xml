<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.meeting.mapper.MeetingParticipantsMapper">

    <select id="getMeetingParticipantsList"
            resultType="com.ruoyi.project.committee.meeting.domain.vo.MeetingParticipantsVo">
        SELECT
            mp.id,
            mp.meeting_id,
            mp.people_id,
            mp.is_attend,
            IF(mp.is_attend, '参加', '不参加') AS is_attend_str,
            mp.no_attend_reason,
            cm.user_name AS people_name,
            cm.unit_post
        FROM
            meeting_participants mp
        LEFT JOIN sys_committee_member cm ON mp.people_id = cm.id
        WHERE mp.del_flag = false
            AND mp.meeting_id = #{searchDto.meetingId}
        <if test="searchDto.peopleName != null and searchDto.peopleName != ''">
            AND cm.user_name LIKE CONCAT('%', #{searchDto.peopleName}, '%')
        </if>
    </select>

    <select id="getParticipantsListByMeetingId"
            resultType="com.ruoyi.project.committee.meeting.domain.vo.MeetingParticipantsVo">
        SELECT
            mp.id,
            mp.meeting_id,
            mp.people_id,
            mp.is_attend,
            cm.user_name AS people_name,
            cm.unit_post
        FROM
            meeting_participants mp
        LEFT JOIN sys_committee_member cm ON mp.people_id = cm.id
        WHERE mp.del_flag = false
            AND mp.meeting_id = #{meetingId}
    </select>

    <select id="getMeetingParticipantsByUserId"
            resultType="com.ruoyi.project.committee.meeting.domain.MeetingParticipants">
        SELECT
            mp.*
        FROM
            meeting_participants mp
        LEFT JOIN sys_committee_member cm ON mp.people_id = cm.id
        WHERE mp.del_flag = false
            AND meeting_id = #{meetingId}
            AND cm.user_id = #{userId}
    </select>
</mapper>
