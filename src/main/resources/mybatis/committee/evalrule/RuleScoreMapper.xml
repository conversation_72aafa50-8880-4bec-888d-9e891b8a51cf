<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.evalrule.mapper.RuleScoreMapper">

    <insert id="upsertRuleScoresBatch" parameterType="list">
        INSERT INTO rule_score (
            id,
            year,
            user_id,
            user_name,
            number_id,
            unit_post,
            basic_score,
            reward_score,
            total_score,
            score_detail,
            create_time,
            update_time,
            del_flag
        ) VALUES
        <foreach collection="ruleScoreList" item="item" separator=",">
            (
                #{item.id},
                #{item.year},
                #{item.userId},
                #{item.userName},
                #{item.numberId},
                #{item.unitPost},
                #{item.basicScore},
                #{item.rewardScore},
                #{item.totalScore},
                #{item.scoreDetail},
                NOW(),
                NOW(),
                0
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            basic_score = VALUES(basic_score),
            reward_score = VALUES(reward_score),
            total_score = VALUES(total_score),
            score_detail = VALUES(score_detail),
            update_time = NOW()
    </insert>
</mapper>