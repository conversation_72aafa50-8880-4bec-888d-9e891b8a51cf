<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper">


    <select id="getMemberInfo" resultType="com.ruoyi.project.committee.archive.domain.CommitteeMember">
        SELECT
            cm.id,
            cm.year,
            cm.user_id,
            cm.user_name,
            cm.number_id,
            IF(cm.sex='0', '男', '女') AS sex,
            n.name AS nation,
            cm.birth,
            cm.education,
            cm.unit_post,
            cm.unit_phone,
            cm.unit_zip_code,
            cm.unit_address,
            cm.home_tel,
            cm.home_zip_code,
            cm.home_address,
            cm.my_phone,
            cm.CateGory,
            cm.image_src,
            cm.elected_period,
            cm.is_enable,
            cm.status,
            cm.origin_place,
            cm.party,
            ss.sector_name AS sector,
            cm.sector_call_type,
            cm.degree,
            cm.professional_ranks,
            cm.speciality,
            cm.city,
            cm.city_call_type,
            cm.country,
            cm.territoriality,
            cm.is_standing_committee,
            cm.is_supervision,
            cm.cppcc_post,
            d.dept_name AS belongs_special_committee,
            cm.special_committee_post,
            cm.democratic_parties_post,
            cm.training,
            cm.reward,
            cm.punishment,
            cm.social_organization_post,
            cm.invention,
            cm.link_man,
            cm.link_phone,
            cm.link_tel,
            cm.link_address,
            cm.link_mail,
            cm.mailing_address,
            cm.identification_id,
            cm.introduction,
            cm.sort,
            cm.special_xommittee_post,
            cm.administrative_level,
            cm.is_people_of_the_new_social_class,
            cm.cw_sort,
            cm.my_email,
            cm.Call_Type,
            cm.fax,
            cm.others,
            cm.focus,
            cm.expertise,
            cm.post_mode,
            cm.party_group_type,
            cm.region_id,
            cm.time_in_work,
            cm.serve_time,
            cm.activity_team_post,
            cm.is_autonomous,
            cm.create_by,
            cm.create_time,
            cm.update_by,
            cm.update_time,
            cm.del_flag
        FROM sys_committee_member cm
            LEFT JOIN sys_nation n ON cm.nation = n.code
            LEFT JOIN sys_sector ss ON cm.sector = ss.id
            LEFT JOIN sys_dept d ON cm.belongs_special_committee = d.dept_id
        WHERE cm.user_id = #{id}
          AND cm.`year` = 2024
          AND cm.del_flag = 0
    </select>

    <select id="getMemberIdsByUserId" resultType="java.lang.Long">
        SELECT id
        FROM sys_committee_member
        WHERE user_id = #{userId} AND del_flag = 0
    </select>

    <select id="selectCommitteeMemberByCondition" resultType="com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo">
        SELECT
            cm.id,
            cm.user_id,
            cm.user_name,
            cm.unit_post
        FROM sys_committee_member cm
            LEFT JOIN sys_sector ss ON cm.sector = ss.id
            LEFT JOIN sys_dept d ON cm.belongs_special_committee = d.dept_id
        WHERE cm.del_flag = 0
        <if test="year != null">
            AND cm.year = #{year}
        </if>
        <if test="electedPeriod != null and electedPeriod != ''">
            AND cm.elected_period like CONCAT(#{electedPeriod}, '%')
        </if>
        <if test="electedTimes != null and electedTimes != ''">
            AND cm.elected_period = #{electedTimes}
            <choose>
                <when test="_databaseId == 'mysql'">
                    AND 1=1 /* Using electedTimes: ${electedTimes} */
                </when>
                <otherwise>
                    /* Using electedTimes: ${electedTimes} */
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            AND cm.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="sector != null and sector != ''">
            AND ss.sector_name = #{sector}
        </if>
        <if test="belongsSpecialCommittee != null and belongsSpecialCommittee != ''">
            AND d.dept_name = #{belongsSpecialCommittee}
        </if>
        ORDER BY cm.sort
    </select>

</mapper>