<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.resumption.mapper.ExamineParticipantMapper">

    <select id="selectReportPkIdByCommittee" resultType="java.lang.String">
        SELECT ep.report_pkid
        FROM t_examine_participants ep
            LEFT JOIN sys_committee_member cm ON ep.committee_pkid = cm.id
        WHERE ep.del_flag = false
          <if test="committeeName != null and committeeName != ''">
            AND cm.user_name LIKE CONCAT('%', #{committeeName}, '%')
          </if>
          <choose>
            <when test="period != null and period != '' and rate != null and rate != ''">
                AND cm.elected_period = CONCAT(#{period}, '-', #{rate})
            </when>
            <when test="period != null and period != ''">
                AND cm.elected_period LIKE CONCAT(#{period}, '-%')
            </when>
            <when test="rate != null and rate != ''">
                AND cm.elected_period LIKE CONCAT('%-', #{rate})
            </when>
          </choose>
          <if test="userId != null and userId != ''">
            AND cm.user_id = #{userId}
          </if>
          <if test="belongSpecialCommittee != null and belongSpecialCommittee != ''">
            AND cm.belongs_special_committee = #{belongSpecialCommittee}
          </if>
    </select>

    <select id="selectParticipant" resultType="com.ruoyi.project.committee.resumption.domain.vo.ParticipantVo">
        SELECT
            ep.report_pkid AS pkId,
            ep.committee_pkid AS committeePkId,
            cm.user_name AS committeeName,
            cm.unit_post AS unitPost
        FROM t_examine_participants ep
        LEFT JOIN sys_committee_member cm ON ep.committee_pkid = cm.id
        WHERE ep.del_flag = false AND ep.report_pkid IN
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectParticipantByReportPkId"
            resultType="com.ruoyi.project.committee.resumption.domain.vo.ParticipantVo">
        SELECT
            ep.report_pkid AS pkId,
            ep.committee_pkid AS committeePkId,
            cm.user_name AS committeeName,
            cm.unit_post AS unitPost,
            ep.report_type AS reportType,
            ep.person_type AS personType
        FROM t_examine_participants ep
            LEFT JOIN sys_committee_member cm ON ep.committee_pkid = cm.id
        WHERE ep.del_flag = false AND ep.report_pkid = #{reportPkId}
    </select>
</mapper>