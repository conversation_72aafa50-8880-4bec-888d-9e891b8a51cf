<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.archive.mapper.MemberStatisticsMapper">


    <select id="selectMemberStatisticsPage" resultType="com.ruoyi.project.committee.archive.domain.vo.MemberStatisticsVo">
        SELECT
            m.id,
            m.user_id,
            cm.user_name AS member_name,
            cm.is_standing_committee AS is_scm,
            ss.sector_name AS sector,
            m.general_meeting,
            m.standing_committee,
            m.consultative_conferences,
            m.other_meetings,
            m.proposals,
            m.conf_speech,
            m.opinions,
            m.join_activity,
            m.research_reports,
            m.publish_article,
            m.key_tasks,
            m.awards,
            m.charity
        FROM member_statistics m
            LEFT JOIN sys_committee_member cm ON m.user_id  = cm.user_id AND cm.`year` = 2024
            LEFT JOIN sys_sector ss ON cm.sector = ss.id
        WHERE m.del_flag = 0 AND m.`year` = 2024
        <if test="pageDto.memberName != null and pageDto.memberName != ''">
            AND cm.user_name LIKE CONCAT('%', #{pageDto.memberName}, '%')
        </if>
        <if test="pageDto.numberId != null and pageDto.numberId != ''">
            AND cm.number_id LIKE CONCAT('%', #{pageDto.numberId}, '%')
        </if>
        <if test="pageDto.sector != null and pageDto.sector != ''">
            AND ss.sector_name = #{pageDto.sector}
        </if>
        <if test="pageDto.memberSex != null and pageDto.memberSex != ''">
            AND cm.sex = #{pageDto.memberSex}
        </if>
        <if test="pageDto.isScm != null">
            AND cm.is_standing_committee = #{pageDto.isScm}
        </if>

    </select>
</mapper>