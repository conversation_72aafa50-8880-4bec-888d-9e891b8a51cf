<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.activity.mapper.ActivityNotificationMapper">
	<select id="selectNotificationPage" resultType="com.ruoyi.project.activity.domain.vo.ActivityNotificationVO">
	    SELECT
	    	an.id,
	    	an.activity_id,
	    	ab.title AS activity_title,
	    	an.title,
	    	an.content,
			an.is_published,
	    	an.is_send_sms AS is_send_sms,
	    	an.create_by AS create_by,
	    	su1.user_name AS creater_name,
	    	an.create_at,
	    	an.update_by AS update_by,
	    	su2.user_name AS updater_name,
	    	an.update_at 
	    FROM
	    	activity_notification an
	    	LEFT JOIN activity_basicinfo ab ON an.activity_id = ab.id
	    	LEFT JOIN sys_user su1 ON an.create_by = su1.user_id
	    	LEFT JOIN sys_user su2 ON an.update_by = su2.user_id 
	    WHERE
	    	an.del_flag = 0
		<if test="searchDto.activityId != null">
            AND an.activity_id = #{searchDto.activityId}
        </if>
		<if test="searchDto.title != null and searchDto.title != ''">
            AND an.title LIKE CONCAT('%', #{searchDto.title}, '%')
        </if>
		<if test="searchDto.publisherName != null and searchDto.publisherName != ''">
            AND su1.user_name LIKE CONCAT('%', #{searchDto.publisherName}, '%')
        </if>
        ORDER BY
	        an.create_at DESC
	</select>
	<!-- 根据id获取详情 -->
	<select id="selectDetailById" resultType="com.ruoyi.project.activity.domain.vo.ActivityNotificationVO">
		SELECT
			an.id,
			an.activity_id,
			ab.title AS activity_title,
			an.title,
			an.content,
			an.is_published,
			an.is_send_sms AS is_send_sms,
			an.create_by AS create_by,
			su1.user_name AS creater_name,
			an.create_at,
			an.update_by AS update_by,
			su2.user_name AS updater_name,
			an.update_at 
		FROM
			activity_notification an
			LEFT JOIN activity_basicinfo ab ON an.activity_id = ab.id
			LEFT JOIN sys_user su1 ON an.create_by = su1.user_id
			LEFT JOIN sys_user su2 ON an.update_by = su2.user_id 
		WHERE
			an.del_flag = 0
			AND an.id = #{id}
	</select>
</mapper>