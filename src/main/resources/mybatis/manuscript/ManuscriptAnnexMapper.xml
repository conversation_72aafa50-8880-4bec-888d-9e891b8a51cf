<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.community.mapper.ManuscriptAnnexMapper">

    <select id="getAnnexList" resultType="com.ruoyi.project.proposal.domain.vo.AnnexVo">
        SELECT
            a.id,
            a.url,
            a.annex_name,
            a.annex_type
        FROM manuscript_annex_rel r
            INNER JOIN annex a ON r.annex_id = a.id
        WHERE r.manuscript_id = #{manuscriptId}
            AND r.del_flag = false
    </select>
</mapper>