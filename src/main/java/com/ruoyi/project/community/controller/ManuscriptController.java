package com.ruoyi.project.community.controller;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.enums.manuscript.ManuscriptStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.community.domain.dto.ManuscriptAuditDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptResponseDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptPublishDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptStatisticsDto;
import com.ruoyi.project.community.domain.vo.*;
import com.ruoyi.project.community.service.IManuscriptService;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 社情民意
 */
@RestController
@RequestMapping("/manuscript")
public class ManuscriptController extends BaseController {

    @Resource
    private IManuscriptService manuscriptService;

    /**
     * 添加稿件
     *
     * @param manuscriptEditVo vo
     * @return id
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ManuscriptEditVo manuscriptEditVo) {
        return AjaxResult.success(manuscriptService.addManuscript(manuscriptEditVo));
    }

    /**
     * 编辑稿件
     * @param manuscriptEditVo vo
     * @return id
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ManuscriptEditVo manuscriptEditVo) {
        return AjaxResult.success(manuscriptService.editManuscript(manuscriptEditVo));
    }

    /**
     * 获取稿件详情
     * @param id 稿件id
     * @return manuscript
     */
    @GetMapping("/get")
    public AjaxResult get(@RequestParam String id) {
        return AjaxResult.success(manuscriptService.getManuscript(id));
    }

    /**
     * 稿件审核
     * @param auditDTO auditDTO
     * @return auditDTO
     */
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody ManuscriptAuditDTO auditDTO) {
        return AjaxResult.success(manuscriptService.auditManuscript(auditDTO));
    }

    /**
     * 稿件弃稿
     * @param auditDTO auditDTO
     * @return auditDTO
     */
    @PostMapping("/discard")
    public AjaxResult discard(@RequestBody ManuscriptAuditDTO auditDTO) {
        auditDTO.getManuscriptEditVo().setStatus(ManuscriptStatusEnum.DISCARD);
        return AjaxResult.success(manuscriptService.discardOrMaterialize(auditDTO));
    }

    /**
     * 稿件素材
     * @param auditDTO auditDTO
     * @return auditDTO
     */
    @PostMapping("/materialize")
    public AjaxResult materialize(@RequestBody ManuscriptAuditDTO auditDTO) {
        auditDTO.getManuscriptEditVo().setStatus(ManuscriptStatusEnum.MATERIAL);
        return AjaxResult.success(manuscriptService.discardOrMaterialize(auditDTO));
    }

    /**
     * 稿件签发
     * @param auditDTO auditDTO
     * @return auditDTO
     */
    @PostMapping("/issue")
    public AjaxResult issue(@RequestBody ManuscriptAuditDTO auditDTO) {
        return AjaxResult.success(manuscriptService.issueManuscript(auditDTO));
    }

    /**
     * 退回修改
     * @param auditDTO auditDTO
     * @return auditDTO
     */
    @PostMapping("/processEdit")
    public AjaxResult processEdit(@RequestBody ManuscriptAuditDTO auditDTO) {
        return AjaxResult.success(manuscriptService.processEditManuscript(auditDTO));
    }



    /**
     * 信息审核分页
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getAuditPage")
    public TableDataInfo getAuditPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptReviewVo> auditPage = manuscriptService.getAuditPage(pageParam);
        return getDataTable(auditPage.getRecords(), auditPage.getTotal());
    }

    /**
     * 信息签发分页
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getIssuePage")
    public TableDataInfo getIssuePage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> pageResult = manuscriptService.getIssuePage(pageParam);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 信息反馈分页
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getFeedbackPage")
    public TableDataInfo getFeedbackPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> pageResult = manuscriptService.getFeedbackPage(pageParam);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 领导批示分页
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getEndorsedPage")
    public TableDataInfo getEndorsedPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> pageResult = manuscriptService.getEndorsedPage(pageParam);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 后期处理分页
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getPostProcessPage")
    public TableDataInfo getPostProcessPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> pageResult = manuscriptService.getPostProcessPage(pageParam);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 社情民意分页
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getPage")
    public TableDataInfo getPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> pageResult = manuscriptService.getPage(pageParam);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 获取我的稿件
     * @param pageParam pageParam
     * @return page
     */
    @PostMapping("/getMyPage")
    public TableDataInfo getMyPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> myPage = manuscriptService.getMyPage(pageParam);
        return getDataTable(myPage.getRecords(), myPage.getTotal());
    }

    /**
     *
     * 信息公开分页
     */
    @PostMapping("/getPublicPage")
    public TableDataInfo getPublicPage(@RequestBody ManuscriptPageParamVo pageParam) {
        IPage<ManuscriptPageVo> pageResult = manuscriptService.getPublicPage(pageParam);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 修改稿件公开状态
     * @param publishDTO id列表
     * @return boolean
     */
    @PostMapping("/publish")
    public AjaxResult publish(@Valid @RequestBody ManuscriptPublishDTO publishDTO)  {
        return AjaxResult.success(manuscriptService.publish(publishDTO));
    }

    /**
     * 获取审核记录
     * @param manuscriptId 稿件id
     * @return auditLog
     */
    @GetMapping("/getAuditLog")
    public TableDataInfo getAuditLog(@RequestParam String manuscriptId) {
        return getDataTable(manuscriptService.getAuditLog(manuscriptId));
    }


    /**
     * 领导反馈
     * @param feedbackDTO dto
     * @return id
     */
    @PostMapping("/feedback")
    public AjaxResult addFeedback(@RequestBody ManuscriptResponseDTO feedbackDTO) {
        return AjaxResult.success(manuscriptService.saveOrUpdateFeedback(feedbackDTO));
    }

    /**
     * 领导批示
     * @param feedbackDTO dto
     * @return id
     */
    @PostMapping("/endorsed")
    public AjaxResult addEndorsement(@RequestBody ManuscriptResponseDTO feedbackDTO) {
        return AjaxResult.success(manuscriptService.saveOrUpdateEndorsement(feedbackDTO));
    }

    /**
     * 领导反馈修改
     * @param feedbackDTO dto
     * @return id
     */
    @PostMapping("/editFeedback")
    public AjaxResult editFeedback(@RequestBody ManuscriptResponseDTO feedbackDTO) {
        return AjaxResult.success(manuscriptService.editFeedback(feedbackDTO));
    }

    /**
     * 领导批示修改
     * @param feedbackDTO dto
     * @return id
     */
    @PostMapping("/editEndorsement")
    public AjaxResult editEndorsement(@RequestBody ManuscriptResponseDTO feedbackDTO) {
        return AjaxResult.success(manuscriptService.editEndorsement(feedbackDTO));
    }



//    /**
//     * 获取领导反馈
//     * @param manuscriptId 稿件id
//     * @return feedback
//     */
//    @GetMapping("/getFeedback")
//    public AjaxResult getFeedback(@RequestParam String manuscriptId) {
//        return AjaxResult.success(manuscriptService.getFeedback(manuscriptId));
//    }
//
//    /**
//     * 获取领导批示
//     * @param manuscriptId 稿件id
//     * @return endorsement
//     */
//    @GetMapping("/getEndorsement")
//    public AjaxResult getEndorsement(@RequestParam String manuscriptId) {
//        return AjaxResult.success(manuscriptService.getEndorsement(manuscriptId));
//    }

    /**
     * 后期处理 - 批量弃稿
     * @param ids id列表
     * @return Boolean
     */
    @PostMapping("/batchDiscard")
    public AjaxResult batchDiscard(@RequestBody List<String> ids) {
        return AjaxResult.success(manuscriptService.batchDiscard(ids));
    }

    /**
     * 后期处理 - 批量删除
     * @param ids id列表
     * @return Boolean
     */
    @PostMapping("/batchDelete")
    public AjaxResult batchDelete(@RequestBody List<String> ids) {
        return AjaxResult.success(manuscriptService.batchDelete(ids));
    }

    /**
     * 后期处理 - 转审核
     * @param feedbackDTO feedbackDTO
     * @return Boolean
     */
    @PostMapping("/toAuditing")
    public AjaxResult toAuditing(@RequestBody ManuscriptResponseDTO feedbackDTO) {
        return AjaxResult.success(manuscriptService.toAuditing(feedbackDTO.getManuscriptId()));
    }

    /**
     * 导出单个稿件
     * @param response response
     * @param manuscriptId manuscriptId
     */
    @GetMapping("/export")
    public void export(HttpServletResponse response, String manuscriptId) {

        Map<String, Object> exportMap = manuscriptService.getExportVo(manuscriptId);

        try {
            ClassPathResource templateFile = new ClassPathResource("template/manuscript_template.docx");
            XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(), exportMap);

            String fileName = exportMap.get("title").toString() + ".docx";
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            ServletOutputStream out = response.getOutputStream();
            document.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.info("导出稿件失败！ >> {}", e.getMessage());
            throw new ServiceException("导出稿件失败！");
        }

    }

    /**
     * 批量导出稿件
     * @param response response
     * @param idList   idList
     */
    @PostMapping("/exportBatch")
    public void exportBatch(HttpServletResponse response, @RequestBody List<String> idList) throws UnsupportedEncodingException {

        if (ObjectUtil.isEmpty(idList)) {
            throw new ServiceException("请选择稿件！");
        }

        List<Map<String, Object>> exportList = manuscriptService.selectExportList(idList);
        if (ObjectUtil.isEmpty(exportList)) {
            throw new ServiceException("稿件不存在！");
        }

        // 设置响应头
        String fileName = DateUtil.today() + ".zip";
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

        ClassPathResource templateFile = new ClassPathResource("template/manuscript_template.docx");
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            for (Map<String, Object> dataMap : exportList) {
                String wordName = dataMap.get("fileName").toString() + ".docx";

                XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(), dataMap);

                ZipEntry zipEntry = new ZipEntry(wordName);
                zos.putNextEntry(zipEntry);

                document.write(zos);
                zos.closeEntry();
            }

            zos.finish();
        } catch (Exception e) {
            logger.info("导出稿件失败！ >> {}", e.getMessage());
            throw new ServiceException("导出稿件失败！");
        }
    }

    /**
     * 导出列表
     * @param response response
     * @param idList idList
     */
    @PostMapping("/exportList")
    public void exportList(HttpServletResponse response, @RequestBody List<String> idList) {

        if (ObjectUtil.isEmpty(idList)) {
            throw new ServiceException("请选择稿件！");
        }

        Map<String, Object> exportMap = manuscriptService.selectDirectoryList(idList);
        if (ObjectUtil.isEmpty(exportMap)) {
            throw new ServiceException("稿件不存在！");
        }

        try {
            ClassPathResource templateFile = new ClassPathResource("template/manuscript_directory_template.docx");
            XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(), exportMap);

            String fileName = "稿件目录.docx";
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            ServletOutputStream out = response.getOutputStream();
            document.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.info("导出稿件目录失败！ >> {}" , e.getMessage());
            throw new ServiceException("导出稿件目录失败！");
        }

    }

    /**
     * 社情民意统计
     * @return result
     */
    @PostMapping("/statistics")
    public TableDataInfo selectManuscriptStatistics(@RequestBody ManuscriptStatisticsDto statisticsDto) {
        return getDataTable(manuscriptService.selectManuscriptStatistics(statisticsDto));
    }

    /**
     * 获取用户稿件
     * @return result
     */
    @PostMapping("/getManuscriptByReflector")
    public TableDataInfo getManuscriptByReflector(@RequestBody ManuscriptStatisticsDto statisticsDto) {
        return getDataTable(manuscriptService.selectManuscriptByReflector(statisticsDto));
    }

}
