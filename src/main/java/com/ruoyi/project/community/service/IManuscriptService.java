package com.ruoyi.project.community.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.community.domain.dto.ManuscriptAuditDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptResponseDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptPublishDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptStatisticsDto;
import com.ruoyi.project.community.domain.vo.*;
import com.ruoyi.project.proposal.domain.dto.ProposalToManuscriptDto;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

public interface IManuscriptService {

    String addManuscript(ManuscriptEditVo editVo);

    String editManuscript(ManuscriptEditVo editVo);

    ManuscriptVo getManuscript(String id);

    IPage<ManuscriptReviewVo> getAuditPage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getMyPage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getPostProcessPage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getPage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getIssuePage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getFeedbackPage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getEndorsedPage(ManuscriptPageParamVo pageParam);

    IPage<ManuscriptPageVo> getPublicPage(ManuscriptPageParamVo pageParam);

    Integer publish(@Valid ManuscriptPublishDTO publishDTO);

    Boolean auditManuscript(ManuscriptAuditDTO auditDTO);

    Boolean issueManuscript(ManuscriptAuditDTO auditDTO);

    Boolean processEditManuscript(ManuscriptAuditDTO auditDTO);

    Boolean discardOrMaterialize(ManuscriptAuditDTO auditDTO);

    List<ManuscriptRecordVo> getAuditLog(String manuscriptId);

    Boolean saveOrUpdateFeedback(ManuscriptResponseDTO responseDTO);

    Boolean saveOrUpdateEndorsement(ManuscriptResponseDTO responseDTO);

    Boolean editFeedback(ManuscriptResponseDTO responseDTO);

    Boolean editEndorsement(ManuscriptResponseDTO responseDTO);

    String getFeedback(String manuscriptId);

    String getEndorsement(String manuscriptId);


    Boolean batchDiscard(List<String> ids);

    Boolean batchDelete(List<String> ids);

    Boolean toAuditing(String manuscriptId);

    Map<String, Object> getExportVo(String manuscriptId);

    List<Map<String, Object>> selectExportList(List<String> idList);

    Map<String, Object> selectDirectoryList(List<String> idList);

    String proposalToManuscript(ProposalToManuscriptDto toManuscriptDto);

    List<ManuscriptStatisticsVo> selectManuscriptStatistics(ManuscriptStatisticsDto statisticsDto);

    List<ManuscriptExportVo> selectManuscriptByReflector(ManuscriptStatisticsDto statisticsDto);
}
