package com.ruoyi.project.community.service;

import com.ruoyi.project.community.domain.ManuscriptAnnex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;

import java.util.List;

public interface IManuscriptAnnexService {

    void saveBatchManuscriptAnnex(String manuscriptId, List<String> annexIdList);

    void updateManuscriptAnnex(String manuscriptId, List<String> annexIdList);

    List<AnnexVo> getAnnexList(String manuscriptId);
}
