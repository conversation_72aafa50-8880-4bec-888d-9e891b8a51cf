package com.ruoyi.project.community.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.enums.manuscript.EndorseTypeEnum;
import com.ruoyi.project.community.domain.ManuscriptEndorsement;
import com.ruoyi.project.community.domain.dto.ManuscriptResponseDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface ManuscriptEndorsementMapper extends BaseMapper<ManuscriptEndorsement> {

    default Boolean existEndorsement(String manuscriptId) {
        return exists(new LambdaQueryWrapper<ManuscriptEndorsement>()
                .eq(ManuscriptEndorsement::getManuscriptId, manuscriptId));
    };

    default ManuscriptEndorsement getManuscriptEndorsement(String manuscriptId) {
        return selectOne(new LambdaQueryWrapper<ManuscriptEndorsement>()
                .eq(ManuscriptEndorsement::getManuscriptId, manuscriptId)
        );
    };

    default void updateEndorsement(ManuscriptResponseDTO responseDTO) {
        String endorseType = responseDTO.getEndorseTypeList().stream()
                .map(EndorseTypeEnum::name)
                .collect(Collectors.joining(","));
        update(null, new LambdaUpdateWrapper<ManuscriptEndorsement>()
                .set(ManuscriptEndorsement::getEndorsement, responseDTO.getText())
                .set(ManuscriptEndorsement::getEndorseType, endorseType)
                .eq(ManuscriptEndorsement::getManuscriptId, responseDTO.getManuscriptId())
        );
    };

    default Map<String, ManuscriptEndorsement> selectEndorsement(List<String> ids) {
        List<ManuscriptEndorsement> endorsementList = selectList(new LambdaQueryWrapper<ManuscriptEndorsement>()
                .in(ManuscriptEndorsement::getManuscriptId, ids));

        return endorsementList.stream().collect(
                (HashMap<String, ManuscriptEndorsement>::new),
                (map, bean) -> map.put(bean.getManuscriptId(), bean),
                HashMap::putAll
        );
    };
}
