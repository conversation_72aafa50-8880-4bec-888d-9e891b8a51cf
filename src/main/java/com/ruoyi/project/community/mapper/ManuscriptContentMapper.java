package com.ruoyi.project.community.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.community.domain.ManuscriptContent;
import com.ruoyi.project.community.domain.vo.ManuscriptEditVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ManuscriptContentMapper extends BaseMapper<ManuscriptContent> {

    default ManuscriptContent getManuscriptContent(String manuscriptId) {
        return selectOne(new LambdaQueryWrapper<ManuscriptContent>()
                .eq(ManuscriptContent::getManuscriptId, manuscriptId));
    }

    default void addManuscriptContent(String manuscriptId, ManuscriptEditVo editVo) {
        ManuscriptContent manuscriptContent = new ManuscriptContent();
        manuscriptContent.setManuscriptId(manuscriptId);
        manuscriptContent.setOriginContent(editVo.getContent());
        manuscriptContent.setLatestContent(editVo.getContent());
        insert(manuscriptContent);
    }

    default void updateManuscriptContent(ManuscriptEditVo editVo) {
        update(null, new LambdaUpdateWrapper<ManuscriptContent>()
                .set(ManuscriptContent::getLatestContent, editVo.getContent())
                .eq(ManuscriptContent::getManuscriptId, editVo.getId()));
    }

    default List<ManuscriptContent> selectBatchManuscriptIds(List<String> idList) {
        return selectList(new LambdaQueryWrapper<ManuscriptContent>()
                .in(ManuscriptContent::getManuscriptId, idList));
    };
}
