package com.ruoyi.project.community.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.community.domain.ManuscriptAnnex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ManuscriptAnnexMapper extends BaseMapper<ManuscriptAnnex> {

    default void deleteByManuscriptId(String manuscriptId) {
        this.delete(new LambdaQueryWrapper<ManuscriptAnnex>()
                .eq(ManuscriptAnnex::getManuscriptId, manuscriptId));
    }

    List<AnnexVo> getAnnexList(String manuscriptId);
}
