package com.ruoyi.project.community.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.community.domain.ManuscriptRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ManuscriptRecordMapper extends BaseMapper<ManuscriptRecord> {

    default List<ManuscriptRecord> getManuscriptRecordList(String manuscriptId) {
        return selectList(new LambdaQueryWrapper<ManuscriptRecord>()
                .eq(ManuscriptRecord::getManuscriptId, manuscriptId)
                .orderByDesc(ManuscriptRecord::getCreateTime));
    };

    default void saveBatchRecord(List<ManuscriptRecord> recordList) {
        for (ManuscriptRecord manuscriptRecord : recordList) {
            insert(manuscriptRecord);
        }
    };

}
