package com.ruoyi.project.community.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.enums.manuscript.ManuscriptStatusEnum;
import com.ruoyi.project.community.domain.Manuscript;
import com.ruoyi.project.community.domain.dto.ManuscriptPublishDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptStatisticsDto;
import com.ruoyi.project.community.domain.vo.ManuscriptPageParamVo;
import com.ruoyi.project.community.domain.vo.ManuscriptReviewVo;
import com.ruoyi.project.community.domain.vo.ManuscriptStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ManuscriptMapper extends BaseMapper<Manuscript> {

    Page<Manuscript> getAuditPage(@Param("page") Page<ManuscriptReviewVo> page,
                                   @Param("pageParam") ManuscriptPageParamVo pageParam);

     default Page<Manuscript> selectManuscriptPage(@Param("pageParam") ManuscriptPageParamVo pageParam) {
         return selectPage(new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize()),
                 new LambdaQueryWrapper<Manuscript>()
                         .eq(ObjectUtil.isNotNull(pageParam.getCategory()), Manuscript::getCategory, pageParam.getCategory())
                         .eq(ObjectUtil.isNotNull(pageParam.getCategoryDetail()), Manuscript::getCategoryDetail, pageParam.getCategoryDetail())
                         .eq(ObjectUtil.isNotNull(pageParam.getIsPublish()), Manuscript::getIsPublish, pageParam.getIsPublish())
                         .eq(ObjectUtil.isNotNull(pageParam.getIsFeedback()), Manuscript::getIsFeedback, pageParam.getIsFeedback())
                         .eq(ObjectUtil.isNotNull(pageParam.getIsEndorsed()), Manuscript::getIsEndorsed, pageParam.getIsEndorsed())
                         .eq(ObjectUtil.isNotNull(pageParam.getStatus()), Manuscript::getStatus, pageParam.getStatus())
                         .ge(ObjectUtil.isNotNull(pageParam.getSubmissionStartTime()), Manuscript::getSubmissionTime, pageParam.getSubmissionStartTime())
                         .le(ObjectUtil.isNotNull(pageParam.getSubmissionEndTime()), Manuscript::getSubmissionTime, pageParam.getSubmissionEndTime())
                         .like(ObjectUtil.isNotEmpty(pageParam.getTitle()), Manuscript::getTitle , pageParam.getTitle())
                         .like(ObjectUtil.isNotEmpty(pageParam.getReflector()), Manuscript::getReflector, pageParam.getReflector())
                         .like(ObjectUtil.isNotEmpty(pageParam.getReflectUnit()), Manuscript::getReflectUnit, pageParam.getReflectUnit())
                         .orderByDesc(Manuscript::getCreateTime)
         );
     };

     default int changeManuscriptPublishStatus(ManuscriptPublishDTO dto) {
         return update(null,
                 new LambdaUpdateWrapper<Manuscript>()
                         .set(Manuscript::getIsPublish, dto.getIsPublish())
                         .in(Manuscript::getId, dto.getIds())
         );
     }

    default void batchDiscard(List<String> ids) {
         update(null,
                 new LambdaUpdateWrapper<Manuscript>()
                         .set(Manuscript::getStatus, ManuscriptStatusEnum.DISCARD)
                         .in(Manuscript::getId, ids)
         );
    };

    default void changeToAuditing(String manuscriptId) {
        update(
                null,
                new LambdaUpdateWrapper<Manuscript>()
                        .set(Manuscript::getStatus, ManuscriptStatusEnum.AUDITING)
                        .eq(Manuscript::getId, manuscriptId)
        );
    };


    List<ManuscriptStatisticsVo> selectManuscriptStatistics(@Param("statisticsDto") ManuscriptStatisticsDto statisticsDto);
}
