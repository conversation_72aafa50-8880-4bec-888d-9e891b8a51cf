package com.ruoyi.project.community.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.enums.manuscript.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ManuscriptPageVo {

    private String id;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("类别")
    private CategoryEnum category;

    @ApiModelProperty("详细类别")
    private CategoryDetailEnum categoryDetail;

    @ApiModelProperty("反映单位")
    private String reflectUnit;

    @ApiModelProperty("反映人")
    private String reflector;

    @ApiModelProperty("报送单位")
    private String reportUnit;

    @ApiModelProperty("主送单位")
    private String recipient;

    @ApiModelProperty("抄送单位")
    private String ccUnit;

    @ApiModelProperty("来稿时间")
    private Date submissionTime;

    @ApiModelProperty("签发期数")
    private String issueNumber;

    @ApiModelProperty("是否公开")
    private Boolean isPublish;

    @ApiModelProperty("是否反馈")
    private Boolean isFeedback;

    @ApiModelProperty("反馈内容")
    private String feedback;

    @ApiModelProperty("是否批示")
    private Boolean isEndorsed;

    @ApiModelProperty("批示内容")
    private String endorsement;

    @ApiModelProperty("批示领导")
    private String endorseLeader;

    @ApiModelProperty("信息来源")
    private SourceEnum source;

    @ApiModelProperty("状态")
    private ManuscriptStatusEnum status;

    @JsonIgnore
    private AdoptWayEnum adoptWay;

    @ApiModelProperty("状态Label")
    private String statusLabel;
}
