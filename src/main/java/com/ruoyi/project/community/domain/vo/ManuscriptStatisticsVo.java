package com.ruoyi.project.community.domain.vo;

import lombok.Data;

@Data
public class ManuscriptStatisticsVo {


    private String userId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 单独
     */
    private Long aloneCount = 0L;

    /**
     * 联名
     */
    private Long jointCount = 0L;

    /**
     * 总数
     */

    private Long totalCount = 0L;


    /**
     * 提交备注
     */
    private String submissionRemark;

    /**
     * 上级政协
     */
    private Long higherCount = 0L;

    /**
     * 市政协
     */
    private Long municipalCount = 0L;

    /**
     * 采用备注
     */
    private String adoptionRemark;


    public void setTotalCount() {
        this.totalCount = this.aloneCount + this.jointCount;
    }
    
}
