package com.ruoyi.project.community.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("manuscript_annex_rel")
public class ManuscriptAnnex extends BaseEntity {

    private String id;

    private String manuscriptId;

    private String annexId;

    @TableLogic
    private Boolean delFlag;
}
