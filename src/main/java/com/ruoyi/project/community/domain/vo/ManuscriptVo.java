package com.ruoyi.project.community.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.manuscript.*;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.system.domain.vo.SysDeptVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ManuscriptVo {

    private String id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "原稿")
    private String originContent;

    @ApiModelProperty(value = "正文")
    private String content;

    @ApiModelProperty(value = "类别")
    private CategoryEnum category;

    @ApiModelProperty(value = "类别明细")
    private CategoryDetailEnum categoryDetail;

    @ApiModelProperty(value = "反映单位")
    private String reflectUnit;

    @ApiModelProperty(value = "反映人")
    private String reflector;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "稿件状态")
    private ManuscriptStatusEnum status;

    @ApiModelProperty(value = "签发期数")
    private String issueNumber;

    @ApiModelProperty(value = "信息来源")
    private SourceEnum source;

    @ApiModelProperty(value = "采用方式")
    private AdoptWayEnum adoptWay;

    @ApiModelProperty(value = "来稿方式")
    private String submitWay;

    @ApiModelProperty(value = "主送单位")
    private String recipient;

    @ApiModelProperty("报送单位")
    private List<ReportUnitEnum> reportUnit;

    @ApiModelProperty("报送单位明细")
    private ReportUnitVo reportUnitDetail;

    @ApiModelProperty("报送对象")
    private List<ReportTargetEnum> reportTarget;

    @ApiModelProperty("其他报送对象")
    private String otherReportTarget;

    @ApiModelProperty(value = "抄送单位")
    private String ccUnit;

    @ApiModelProperty(value = "报送类别")
    private ReportTypeEnum reportType;

    @ApiModelProperty(value = "抄送")
    private String carbonCopy;

    @ApiModelProperty(value = "是否反馈")
    private Boolean isFeedback;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "来稿时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date submissionTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "各委办厅局列表")
    private List<SysDeptVo> wbHall;

    @ApiModelProperty(value = "反映人列表")
    private List<ManuscriptReflectorVo> reflectorList;

    @ApiModelProperty(value = "附件列表")
    private List<AnnexVo> annexList;
}
