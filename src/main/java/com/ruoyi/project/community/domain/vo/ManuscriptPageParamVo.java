package com.ruoyi.project.community.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.manuscript.CategoryDetailEnum;
import com.ruoyi.common.enums.manuscript.CategoryEnum;
import com.ruoyi.common.enums.manuscript.ManuscriptStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ManuscriptPageParamVo {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    private String userId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "稿件类型")
    private CategoryEnum category;

    @ApiModelProperty(value = "详细类型")
    private CategoryDetailEnum categoryDetail;

    @ApiModelProperty(value = "反映人")
    private String reflector;

    @ApiModelProperty(value = "反映单位")
    private String reflectUnit;

    @ApiModelProperty(value = "来稿开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submissionStartTime;

    @ApiModelProperty(value = "来稿结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submissionEndTime;

    @ApiModelProperty(value = "是否公开")
    private Boolean isPublish;

    @ApiModelProperty(value = "是否反馈")
    private Boolean isFeedback;

    @ApiModelProperty(value = "是否批示")
    private Boolean isEndorsed;

    @ApiModelProperty(value = "稿件状态")
    private ManuscriptStatusEnum status;
}
