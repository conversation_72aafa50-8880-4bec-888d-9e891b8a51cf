package com.ruoyi.project.community.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.manuscript.SourceEnum;
import lombok.Data;

import java.util.Date;

@Data
public class ManuscriptReviewVo {

    private String id;

    private String title;

    private String category;

    private String reflectUnit;

    private String reflector;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date submissionTime;

    private SourceEnum source;

    private String status;
}
