package com.ruoyi.project.community.domain.dto;

import com.ruoyi.project.community.domain.vo.ManuscriptEditVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ManuscriptAuditDTO {

    @ApiModelProperty(value = "审核环节")
    private String auditPhase;

    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(value = "稿件详情")
    private ManuscriptEditVo manuscriptEditVo;


    public String getManuscriptId() {
        return manuscriptEditVo.getId();
    }
}
