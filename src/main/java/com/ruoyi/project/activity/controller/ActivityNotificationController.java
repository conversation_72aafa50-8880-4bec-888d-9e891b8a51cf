package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationVO;
import com.ruoyi.project.activity.service.IActivityNotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 活动通知控制器
 */
@Api(tags = "活动通知管理")
@RestController
@RequestMapping("/activity/notification")
public class ActivityNotificationController extends BaseController {

    @Autowired
    private IActivityNotificationService activityNotificationService;

    /**
     * 分页获取活动通知列表
     *
     * @param dto 查询参数
     * @return 通知列表
     */
    @ApiOperation("分页获取活动通知列表")
    @PreAuthorize("@ss.hasPermi('activity:notification:list')")
    @PostMapping("/page")
    public TableDataInfo page(@Validated @RequestBody ActivityNotificationPageDTO dto) {
        IPage<ActivityNotificationVO> result = activityNotificationService.getNotificationPage(dto);
        return getDataTable(result.getRecords(), result.getTotal());
    }

    /**
     * 获取活动通知详情
     *
     * @param id 通知ID
     * @return 通知详情
     */
    @ApiOperation("获取活动通知详情")
    @PreAuthorize("@ss.hasPermi('activity:notification:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(
            @ApiParam(value = "通知ID", required = true) @PathVariable String id) {
        ActivityNotificationVO vo = activityNotificationService.getNotificationById(id);
        return success(vo);
    }

    /**
     * 新增活动通知
     *
     * @param dto 新增参数
     * @return 操作结果
     */
    @ApiOperation("新增活动通知")
    @PreAuthorize("@ss.hasPermi('activity:notification:add')")
    @Log(title = "活动通知管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ActivityNotificationAddDTO dto) {
        return toAjax(activityNotificationService.addNotification(dto));
    }

    /**
     * 修改活动通知
     *
     * @param dto 修改参数
     * @return 操作结果
     */
    @ApiOperation("修改活动通知")
    @PreAuthorize("@ss.hasPermi('activity:notification:edit')")
    @Log(title = "活动通知管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult update(@Validated @RequestBody ActivityNotificationUpdateDTO dto) {
        return toAjax(activityNotificationService.updateNotification(dto));
    }

    /**
     * 删除活动通知
     *
     * @param id 通知ID
     * @return 操作结果
     */
    @ApiOperation("删除活动通知")
    @PreAuthorize("@ss.hasPermi('activity:notification:remove')")
    @Log(title = "活动通知管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(
            @ApiParam(value = "通知ID", required = true) @PathVariable String id) {
        return toAjax(activityNotificationService.deleteNotification(id));
    }
}
