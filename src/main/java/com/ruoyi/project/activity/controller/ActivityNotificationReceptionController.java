package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationReceptionPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationReceptionVO;
import com.ruoyi.project.activity.service.IActivityNotificationReceptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 活动通知接收情况控制器
 */
@Api(tags = "活动通知接收情况管理")
@RestController
@RequestMapping("/activity/notification/reception")
public class ActivityNotificationReceptionController extends BaseController {

    @Autowired
    private IActivityNotificationReceptionService activityNotificationReceptionService;

    /** 分页获取活动通知接收情况列表 */
    @ApiOperation("分页获取活动通知接收情况列表")
    @PreAuthorize("@ss.hasPermi('activity:notification:reception:list')")
    @PostMapping("/page")
    public TableDataInfo page(@RequestBody ActivityNotificationReceptionPageDTO dto) {
        IPage<ActivityNotificationReceptionVO> result = activityNotificationReceptionService
                .getActivityNotificationReceptionsPage(dto);
        return getDataTable(result.getRecords(), result.getTotal());
    }

    /**
     * 标记通知为已接收
     *
     * @param notificationId 通知ID
     * @return 操作结果
     */
    @ApiOperation("标记通知为已接收")
    @Log(title = "活动通知接收情况", businessType = BusinessType.UPDATE)
    @PostMapping("/mark-received/{notificationId}")
    public AjaxResult markAsReceived(
            @ApiParam(value = "通知ID", required = true) @PathVariable Long notificationId) {
        // 获取当前登录用户ID作为接收人ID
        Long recipientId = getUserId();
        return toAjax(activityNotificationReceptionService.markAsReceived(notificationId, recipientId));
    }

    /**
     * 重置通知接收状态
     *
     * @param notificationId 通知ID
     * @param activityId     活动ID
     * @return 操作结果
     */
    @ApiOperation("重置通知接收状态")
    @PreAuthorize("@ss.hasPermi('activity:notification:edit')")
    @Log(title = "活动通知接收情况", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{notificationId}/{activityId}")
    public AjaxResult resetReceptionStatus(
            @ApiParam(value = "通知ID", required = true) @PathVariable Long notificationId,
            @ApiParam(value = "活动ID", required = true) @PathVariable Long activityId) {
        return toAjax(activityNotificationReceptionService.updateNotificationReceptions(notificationId, activityId));
    }
}