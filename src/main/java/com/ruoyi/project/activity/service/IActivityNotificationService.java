package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivityNotification;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationVO;

/**
 * 活动通知服务接口
 */
public interface IActivityNotificationService extends IService<ActivityNotification> {

    /**
     * 分页查询活动通知列表
     *
     * @param dto 查询参数
     * @return 活动通知分页列表
     */
    IPage<ActivityNotificationVO> getNotificationPage(ActivityNotificationPageDTO dto);

    /**
     * 根据ID获取活动通知详情
     *
     * @param id 通知ID
     * @return 活动通知详情
     */
    ActivityNotificationVO getNotificationById(String id);

    /**
     * 新增活动通知
     *
     * @param dto 新增参数
     * @return 是否成功
     */
    boolean addNotification(ActivityNotificationAddDTO dto);

    /**
     * 更新活动通知
     *
     * @param dto 更新参数
     * @return 是否成功
     */
    boolean updateNotification(ActivityNotificationUpdateDTO dto);

    /**
     * 删除活动通知
     *
     * @param id 通知ID
     * @return 是否成功
     */
    boolean deleteNotification(String id);

}