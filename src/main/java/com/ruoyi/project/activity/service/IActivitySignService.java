package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivitySign;
import com.ruoyi.project.activity.domain.dto.ActivitySignAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignListDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySignVO;

/**
 * 活动签到服务接口
 */
public interface IActivitySignService extends IService<ActivitySign> {

    /**
     * 根据活动ID查询签到记录列表（分页）
     *
     * @param dto 查询参数
     * @return 签到记录分页列表
     */
    IPage<ActivitySignVO> getSignListByActivityId(ActivitySignListDTO dto);

    /**
     * 新增活动签到信息
     *
     * @param dto 新增签到参数
     * @return 是否成功
     */
    boolean addActivitySign(ActivitySignAddDTO dto);
}