package com.ruoyi.project.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.activity.domain.ActivityNotificationReception;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationReceptionPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationReceptionVO;
import com.ruoyi.project.activity.mapper.ActivityNotificationReceptionMapper;
import com.ruoyi.project.activity.mapper.ActivityParticipantsMapper;
import com.ruoyi.project.activity.service.IActivityNotificationReceptionService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活动通知接收情况服务实现类
 */
@Service
public class ActivityNotificationReceptionServiceImpl
        extends ServiceImpl<ActivityNotificationReceptionMapper, ActivityNotificationReception>
        implements IActivityNotificationReceptionService {

    @Autowired
    private ActivityNotificationReceptionMapper activityNotificationReceptionMapper;

    @Autowired
    private ActivityParticipantsMapper activityParticipantsMapper;

    /**
     * 根据通知ID查询接收情况列表
     *
     * @param notificationId 通知ID
     * @return 接收情况列表
     */
    @Override
    public IPage<ActivityNotificationReceptionVO> getActivityNotificationReceptionsPage(
            ActivityNotificationReceptionPageDTO searchDto) {
        // 设置页码
        searchDto.setPageNo(searchDto.getCurrentPage());

        // 创建分页对象
        Page<ActivityNotificationReceptionVO> page = new Page<>(searchDto.getCurrentPage(), searchDto.getPageSize());

        // 查询数据
        IPage<ActivityNotificationReceptionVO> result = activityNotificationReceptionMapper
                .selectNotificationReceptionPage(page,
                        searchDto);

        return result;
    }

    /**
     * 批量新增活动通知接收情况列表
     * 
     * @return 是否成功
     */
    public boolean batchCreateNotificationReceptions(Long activityId, Long notificationId) {
        // 校验一下参数
        if (StringUtils.isBlank(String.valueOf(activityId))) {
            throw new ServiceException("活动id不能为空");
        }

        if (StringUtils.isBlank(String.valueOf(notificationId))) {
            throw new ServiceException("通知id不能为空");
        }

        // 获取活动参与人员列表
        List<ActivityParticipants> participants = activityParticipantsMapper.selectList(
                new LambdaQueryWrapper<ActivityParticipants>()
                        .eq(ActivityParticipants::getActivityPkid, activityId.toString())
        // .eq(ActivityParticipants::getIsAttend, "1") // 只选择参加的人员
        );

        if (participants.isEmpty()) {
            return true; // 没有参与人员，视为成功
        }

        // 构建需要插入的数据
        List<ActivityNotificationReception> receptionList = new ArrayList<>();
        for (ActivityParticipants participant : participants) {
            ActivityNotificationReception reception = new ActivityNotificationReception();
            reception.setNotificationId(notificationId);
            reception.setRecipientId(Long.valueOf(participant.getPepolePkid()));
            reception.setReceiveStatus(false); // 初始状态为未接收
            reception.setCreateBy(SecurityUtils.getUserId());
            reception.setCreateAt(new Date());
            reception.setDelFlag(false);
            receptionList.add(reception);
        }

        // 批量保存
        return this.saveBatch(receptionList);
    }

    /**
     * 批量更新活动通知接收情况列表
     * 
     * @return 是否成功
     */
    public boolean batchUpdateNotificationReceptions(Long activityId) {
        // 校验一下参数
        if (StringUtils.isBlank(String.valueOf(activityId))) {
            throw new ServiceException("活动id不能为空");
        }

        // 所有的通知列表
        List<Long> notificationIds = new ArrayList<>();
        notificationIds = activityNotificationReceptionMapper.selectNotificationIdsByActivityId(activityId);

        if (!notificationIds.isEmpty()) {
            // 获取活动参与人员列表
            List<ActivityParticipants> participants = activityParticipantsMapper.selectList(
                    new LambdaQueryWrapper<ActivityParticipants>()
                            .eq(ActivityParticipants::getActivityPkid, activityId)
            // .eq(ActivityParticipants::getIsAttend, "1") // 只选择参加的人员
            );
            // 构建需要插入的数据
            List<ActivityNotificationReception> receptions = new ArrayList<>();
            for (ActivityParticipants participant : participants) {
                for (Long notificationId : notificationIds) {
                    ActivityNotificationReception reception = new ActivityNotificationReception();
                    reception.setNotificationId(notificationId);
                    reception.setRecipientId(Long.valueOf(participant.getPepolePkid()));
                    reception.setReceiveStatus(false); // 初始状态为未接收
                    reception.setCreateBy(SecurityUtils.getUserId());
                    reception.setCreateAt(new Date());
                    reception.setUpdateBy(SecurityUtils.getUserId());
                    reception.setUpdateAt(new Date());
                    reception.setDelFlag(false);
                    receptions.add(reception);
                }
            }

            // 插入或更新新的数据
            activityNotificationReceptionMapper.deleteByNotificationIds(notificationIds,
                    SecurityUtils.getUserId());
            return activityNotificationReceptionMapper.saveOrUpdateBatch(receptions) >= 0;
        }
        // 没有可更新的活动通知接收情况
        return true;
    }

    /**
     * 批量删除接收情况
     *
     * @param notificationIds 通知ID
     * @return 是否成功
     */
    public boolean deleteNotificationReceptions(List<Long> notificationIds) {
        return activityNotificationReceptionMapper.deleteByNotificationIds(notificationIds,
                SecurityUtils.getUserId()) >= 0;
    }

    /**
     * 删除活动通知接收情况记录(物理删除)
     *
     * @param notificationId 通知ID
     * @return 是否成功
     */
    public boolean deleteNotificationReceptionsByNotificationId(Long notificationId) {
        if (StringUtils.isBlank(String.valueOf(notificationId))) {
            throw new ServiceException("通知id不能为空");
        }
        Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("notification_id", notificationId);
        return this.removeByMap(columnMap);
    }

    /**
     * 创建活动通知接收情况记录
     * 根据活动ID获取所有参与人员，为每个参与人员创建接收记录
     *
     * @param notificationId 通知ID
     * @param activityId     活动ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createNotificationReceptions(Long notificationId, Long activityId) {
        // 获取活动参与人员列表
        List<ActivityParticipants> participants = activityParticipantsMapper.selectList(
                new LambdaQueryWrapper<ActivityParticipants>()
                        .eq(ActivityParticipants::getActivityPkid, activityId)
                        .eq(ActivityParticipants::getIsAttend, "1") // 只选择参加的人员
        );

        if (participants.isEmpty()) {
            return true; // 没有参与人员，视为成功
        }

        // 当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        Date now = new Date();

        // 批量插入接收记录
        List<ActivityNotificationReception> receptionList = new ArrayList<>(participants.size());
        for (ActivityParticipants participant : participants) {
            ActivityNotificationReception reception = new ActivityNotificationReception();
            reception.setNotificationId(notificationId);
            reception.setRecipientId(Long.valueOf(participant.getPepolePkid()));
            reception.setReceiveStatus(false); // 初始状态为未接收
            reception.setCreateBy(currentUserId);
            reception.setCreateAt(now);
            reception.setDelFlag(false);
            receptionList.add(reception);
        }

        // 批量保存
        return this.saveBatch(receptionList);
    }

    /**
     * 删除活动通知接收情况记录（逻辑删除）
     *
     * @param notificationId 通知ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotificationReceptions(Long notificationId) {
        // 当前用户ID
        Long currentUserId = SecurityUtils.getUserId();

        // 逻辑删除所有接收记录
        int rows = activityNotificationReceptionMapper.logicDeleteByNotificationId(notificationId, currentUserId);
        return rows >= 0; // 即使没有记录被删除也视为成功
    }

    /**
     * 更新活动通知接收情况记录
     * 将所有非逻辑删除的记录的接收状态重置为未接收
     *
     * @param notificationId 通知ID
     * @param activityId     活动ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationReceptions(Long notificationId, Long activityId) {
        // 获取活动参与人员列表
        List<ActivityParticipants> participants = activityParticipantsMapper.selectList(
                new LambdaQueryWrapper<ActivityParticipants>()
                        .eq(ActivityParticipants::getActivityPkid, activityId)
                        .eq(ActivityParticipants::getIsAttend, "1") // 只选择参加的人员
        );

        if (participants.isEmpty()) {
            return true; // 没有参与人员，视为成功
        }

        // 当前用户ID
        Long currentUserId = SecurityUtils.getUserId();

        // 提取参与人员ID列表
        List<Long> participantIds = participants.stream()
                .map(p -> Long.valueOf(p.getPepolePkid()))
                .collect(Collectors.toList());

        // 重置接收状态
        int rows = activityNotificationReceptionMapper.resetReceiveStatusByNotificationIdAndRecipientIds(
                notificationId, participantIds, currentUserId);

        return rows >= 0; // 即使没有记录被更新也视为成功
    }

    /**
     * 更新活动参与人员后，更新该活动所有通知的接收情况
     * 1. 保留原有参与人员的接收记录，接收状态不变
     * 2. 恢复被删除的参与人员的接收记录（如果存在）
     * 3. 逻辑删除不再参与的人员的接收记录
     * 4. 为新增的参与人员创建接收记录
     *
     * @param activityId     活动ID
     * @param participantIds 新的活动参与人员ID列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReceptionsAfterParticipantsChanged(Long activityId, List<Long> participantIds) {
        // 获取活动的所有通知ID
        List<Long> notificationIds = activityNotificationReceptionMapper.selectNotificationIdsByActivityId(activityId);
        if (notificationIds.isEmpty()) {
            return true; // 没有通知，视为成功
        }

        // 当前用户ID
        Long currentUserId = SecurityUtils.getUserId();
        Date now = new Date();

        // 处理每个通知
        for (Long notificationId : notificationIds) {
            // 1. 查询当前通知的所有接收记录（包括已删除的）
            List<ActivityNotificationReception> allReceptions = this.list(
                    new LambdaQueryWrapper<ActivityNotificationReception>()
                            .eq(ActivityNotificationReception::getNotificationId, notificationId));

            // 将接收记录按接收人ID分组
            Map<Long, ActivityNotificationReception> receptionMap = allReceptions.stream()
                    .collect(Collectors.toMap(
                            ActivityNotificationReception::getRecipientId,
                            reception -> reception,
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            // 2. 处理现有参与人员
            List<Long> existingRecipientIds = new ArrayList<>();
            List<Long> deletedRecipientIds = new ArrayList<>();
            List<Long> newRecipientIds = new ArrayList<>();

            // 分类参与人员
            for (Long participantId : participantIds) {
                if (receptionMap.containsKey(participantId)) {
                    ActivityNotificationReception reception = receptionMap.get(participantId);
                    if (reception.getDelFlag()) {
                        // 已删除的记录需要恢复
                        deletedRecipientIds.add(participantId);
                    } else {
                        // 现有记录保持不变
                        existingRecipientIds.add(participantId);
                    }
                } else {
                    // 新增的参与人员
                    newRecipientIds.add(participantId);
                }
            }

            // 找出需要删除的参与人员（在原记录中存在但不在新参与人员列表中的）
            List<Long> toDeleteRecipientIds = receptionMap.keySet().stream()
                    .filter(id -> !participantIds.contains(id) && !receptionMap.get(id).getDelFlag())
                    .collect(Collectors.toList());

            // 3. 恢复被删除的参与人员的记录
            if (!deletedRecipientIds.isEmpty()) {
                activityNotificationReceptionMapper.restoreDeletedByNotificationIdAndRecipientIds(
                        notificationId, deletedRecipientIds, currentUserId);
            }

            // 4. 逻辑删除不再参与的人员的记录
            if (!toDeleteRecipientIds.isEmpty()) {
                activityNotificationReceptionMapper.logicDeleteByNotificationIdAndRecipientIds(
                        notificationId, toDeleteRecipientIds, currentUserId);
            }

            // 5. 为新增的参与人员创建接收记录
            if (!newRecipientIds.isEmpty()) {
                List<ActivityNotificationReception> newReceptions = new ArrayList<>(newRecipientIds.size());
                for (Long recipientId : newRecipientIds) {
                    ActivityNotificationReception reception = new ActivityNotificationReception();
                    reception.setNotificationId(notificationId);
                    reception.setRecipientId(recipientId);
                    reception.setReceiveStatus(false); // 初始状态为未接收
                    reception.setCreateBy(currentUserId);
                    reception.setCreateAt(now);
                    reception.setDelFlag(false);
                    newReceptions.add(reception);
                }
                this.saveBatch(newReceptions);
            }
        }

        return true;
    }

    /**
     * 标记通知为已接收
     *
     * @param notificationId 通知ID
     * @param recipientId    接收人ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsReceived(Long notificationId, Long recipientId) {
        // 查询接收记录
        ActivityNotificationReception reception = this.getOne(
                new LambdaQueryWrapper<ActivityNotificationReception>()
                        .eq(ActivityNotificationReception::getNotificationId, notificationId)
                        .eq(ActivityNotificationReception::getRecipientId, recipientId)
                        .eq(ActivityNotificationReception::getDelFlag, false));

        if (reception == null) {
            return false; // 记录不存在
        }

        // 已经是已接收状态，无需更新
        if (reception.getReceiveStatus()) {
            return true;
        }

        // 更新为已接收
        reception.setReceiveStatus(true);
        reception.setReceiveTime(new Date());
        reception.setUpdateBy(SecurityUtils.getUserId());
        reception.setUpdateAt(new Date());

        return this.updateById(reception);
    }
}
