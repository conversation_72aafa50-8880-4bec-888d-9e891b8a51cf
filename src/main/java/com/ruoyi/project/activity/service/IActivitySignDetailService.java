package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivitySignDetail;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailBatchDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailLeaveDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailPageDTO;
import com.ruoyi.project.activity.domain.dto.UpdateActivitySignDetailDTO;
import com.ruoyi.project.activity.domain.dto.UserActivitySignDetailDTO;
import com.ruoyi.project.activity.domain.dto.UserUpdateActivitySignDetailStatusDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySignDetailVO;
import com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO;

/**
 * 活动签到明细服务接口
 */
public interface IActivitySignDetailService extends IService<ActivitySignDetail> {

    /**
     * 根据签到ID分页查询签到明细列表
     *
     * @param dto 查询参数
     * @return 签到明细分页列表
     */
    IPage<ActivitySignDetailVO> getSignDetailPage(ActivitySignDetailPageDTO dto);

    /**
     * 批量签到
     *
     * @param dto 批量签到参数
     * @return 签到成功的数量
     */
    int batchSignIn(ActivitySignDetailBatchDTO dto);

    /**
     * 请假
     *
     * @param dto 请假参数
     * @return 是否成功
     */
    boolean leaveRequest(ActivitySignDetailLeaveDTO dto);

    /**
     * 获取当前用户指定活动的签到详情
     *
     * @param dto 查询参数
     * @return 用户活动签到详情分页列表
     */
    IPage<UserActivitySignDetailVO> getCurrentUserActivitySignDetails(UserActivitySignDetailDTO dto);

    /**
     * 当前登录用户指定参加/不参加/请假
     *
     * @param dto 更新参数
     * @return 是否成功
     */
    boolean updateUserActivitySignDetails(UpdateActivitySignDetailDTO dto);

    /**
     * 当前登录用户签到/请假签到
     * 
     * @param dto 更新参数
     * @return 是否成功
     */
    boolean updateUserActivitySignDetailsStatus(UserUpdateActivitySignDetailStatusDTO dto);

    /**
     * 重置签到状态
     * 
     * @param signDetailId 签到明细ID
     * @return 是否成功
     */
    boolean resetSignStatus(String signDetailId);

}