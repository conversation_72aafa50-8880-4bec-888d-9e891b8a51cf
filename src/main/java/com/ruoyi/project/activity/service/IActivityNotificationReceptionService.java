package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivityNotificationReception;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationReceptionPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationReceptionUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationReceptionVO;

import java.util.List;

/**
 * 活动通知接收情况服务接口
 */
public interface IActivityNotificationReceptionService extends IService<ActivityNotificationReception> {

    /**
     * 根据通知ID查询接收情况列表
     *
     * @param notificationId 通知ID
     * @return 接收情况列表
     */
    IPage<ActivityNotificationReceptionVO> getActivityNotificationReceptionsPage(
            ActivityNotificationReceptionPageDTO searchDto);

    /**
     * 批量新增活动通知接收情况列表
     * 
     */
    boolean batchCreateNotificationReceptions(Long activityId, Long notificationId);

    /**
     * 批量删除接收情况
     *
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean deleteNotificationReceptions(List<Long> notificationIds);

    /**
     * 批量更新接收情况
     * 
     * @return 是否成功
     */
    boolean batchUpdateNotificationReceptions(Long activityId);

    /**
     * 创建活动通知接收情况记录
     *
     * @param notificationId 通知ID
     * @param activityId     活动ID
     * @return 是否成功
     */
    boolean createNotificationReceptions(Long notificationId, Long activityId);

    /**
     * 删除活动通知接收情况记录（逻辑删除）
     *
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean deleteNotificationReceptions(Long notificationId);

    /**
     * 删除活动通知接收情况记录（物理删除）
     *
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean deleteNotificationReceptionsByNotificationId(Long notificationId);

    /**
     * 更新活动通知接收情况记录
     *
     * @param notificationId 通知ID
     * @param activityId     活动ID
     * @return 是否成功
     */
    boolean updateNotificationReceptions(Long notificationId, Long activityId);

    /**
     * 更新活动参与人员后，更新该活动所有通知的接收情况
     *
     * @param activityId     活动ID
     * @param participantIds 新的活动参与人员ID列表
     * @return 是否成功
     */
    boolean updateReceptionsAfterParticipantsChanged(Long activityId, List<Long> participantIds);

    /**
     * 标记通知为已接收
     *
     * @param notificationId 通知ID
     * @param recipientId    接收人ID
     * @return 是否成功
     */
    boolean markAsReceived(Long notificationId, Long recipientId);
}