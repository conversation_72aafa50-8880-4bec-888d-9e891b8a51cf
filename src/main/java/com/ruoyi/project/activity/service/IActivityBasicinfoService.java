package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.dto.ActivityBasicinfoDTO;
import com.ruoyi.project.activity.domain.dto.ActivityBasicinfoPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityBasicinfoVO;
import java.util.List;

/**
 * 活动基本信息服务接口
 */
public interface IActivityBasicinfoService extends IService<ActivityBasicinfo> {

    /**
     * 查询活动基本信息列表
     *
     * @param activityBasicinfo 活动基本信息
     * @return 活动基本信息集合
     */
    public List<ActivityBasicinfo> selectActivityBasicinfoList(ActivityBasicinfo activityBasicinfo);

    /**
     * 查询活动基本信息详细
     *
     * @param id 活动基本信息ID
     * @return 活动基本信息
     */
    public ActivityBasicinfo selectActivityBasicinfoById(Long id);

    /**
     * 新增活动基本信息
     *
     * @param activityBasicinfoDTO 活动基本信息DTO
     * @return 结果
     */
    public int insertActivityBasicinfo(ActivityBasicinfoDTO activityBasicinfoDTO);

    /**
     * 修改活动基本信息
     *
     * @param activityBasicinfoDTO 活动基本信息DTO
     * @return 结果
     */
    public int updateActivityBasicinfo(ActivityBasicinfoDTO activityBasicinfoDTO);

    /**
     * 批量删除活动基本信息
     *
     * @param ids 需要删除的活动基本信息ID数组
     * @return 结果
     */
    public int deleteActivityBasicinfoByIds(Long[] ids);

    /**
     * 删除活动基本信息信息
     *
     * @param id 活动基本信息ID
     * @return 结果
     */
    public int deleteActivityBasicinfoById(Long id);

    /**
     * 获取活动参与人员列表
     *
     * @param activityId 活动ID
     * @return 参与人员ID列表
     */
    public List<String> getActivityParticipants(String activityId);

    /**
     * 分页查询活动列表
     *
     * @param pageDTO 分页查询参数
     * @return 分页结果
     */
    public IPage<ActivityBasicinfoVO> pageActivityBasicinfo(ActivityBasicinfoPageDTO pageDTO);

    /**
     * 批量修改活动发布状态
     *
     * @param ids 活动ID列表
     * @param status 发布状态
     * @return 结果
     */
    public int updateActivityStatusBatch(List<Long> ids, String status);
}