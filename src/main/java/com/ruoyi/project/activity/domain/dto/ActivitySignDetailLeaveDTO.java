package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 活动签到明细请假DTO
 */
@Data
@ApiModel(value = "活动签到明细请假DTO")
public class ActivitySignDetailLeaveDTO {

    /**
     * 签到ID
     */
    @NotBlank(message = "签到ID不能为空")
    @ApiModelProperty(value = "签到ID", required = true)
    private String signPkid;
    
    /**
     * 人员ID
     */
    @NotBlank(message = "人员ID不能为空")
    @ApiModelProperty(value = "人员ID", required = true)
    private String peoplePkid;
    
    /**
     * 请假理由
     */
    @NotBlank(message = "请假理由不能为空")
    @ApiModelProperty(value = "请假理由", required = true)
    private String reason;
}
