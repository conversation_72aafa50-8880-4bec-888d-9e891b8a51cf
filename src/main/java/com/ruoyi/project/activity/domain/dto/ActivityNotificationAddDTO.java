package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增活动通知DTO
 */
@Data
@ApiModel(value = "新增活动通知DTO")
public class ActivityNotificationAddDTO {

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    @NotBlank(message = "活动ID不能为空")
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityId;

    /**
     * 通知标题
     */
    @NotBlank(message = "通知标题不能为空")
    @ApiModelProperty(value = "通知标题", required = true)
    private String title;

    /**
     * 通知内容
     */
    @NotBlank(message = "通知内容不能为空")
    @ApiModelProperty(value = "通知内容", required = true)
    private String content;

    /**
     * 是否发送短信
     */
    @ApiModelProperty(value = "是否发送短信")
    private Boolean isSendSms = false;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人userId")
    private String publisherId;
}