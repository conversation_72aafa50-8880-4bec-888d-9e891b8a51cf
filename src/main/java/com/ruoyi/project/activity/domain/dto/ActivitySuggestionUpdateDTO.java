package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改意见建议DTO
 */
@Data
@ApiModel(value = "修改意见建议DTO")
public class ActivitySuggestionUpdateDTO {

    /**
     * 意见建议ID
     */
    @NotNull(message = "意见建议ID不能为空")
    @ApiModelProperty(value = "意见建议ID", required = true)
    private Long id;

    /**
     * 关联活动ID
     */
    @NotNull(message = "活动ID不能为空")
    @ApiModelProperty(value = "关联活动ID", required = true)
    private Long activityId;

    /**
     * 意见标题
     */
    @NotBlank(message = "意见标题不能为空")
    @ApiModelProperty(value = "意见标题", required = true)
    private String title;

    /**
     * 意见关键词
     */
    @NotBlank(message = "意见关键词不能为空")
    @ApiModelProperty(value = "意见关键词", required = true)
    private String keywords;

    /**
     * 意见内容
     */
    @NotBlank(message = "意见内容不能为空")
    @ApiModelProperty(value = "意见内容", required = true)
    private String content;

    /**
     * 发表人ID
     */
    @NotBlank(message = "发表人ID不能为空")
    @ApiModelProperty(value = "发表人ID", required = true)
    private String submitterId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 审核状态(0-待审核,1-审核通过,2-审核不通过)
     */
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;
}
