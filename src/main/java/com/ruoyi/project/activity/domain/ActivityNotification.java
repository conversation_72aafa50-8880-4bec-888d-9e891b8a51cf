package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 活动通知实体类
 */
@Data
@TableName("activity_notification")
public class ActivityNotification {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 活动ID
     */
    @TableField("activity_id")
    private String activityId;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 是否发送短信
     */
    @TableField("is_send_sms")
    private Boolean isSendSms;

    /** 通知是否已发布 */
    @TableField("is_published")
    private Boolean isPublished;

    /**
     * 创建人ID
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_at")
    private Date createAt;

    /**
     * 更新人ID
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_at")
    private Date updateAt;

    /**
     * 逻辑删除标志位
     */
    @TableField("del_flag")
    private Boolean delFlag;
}