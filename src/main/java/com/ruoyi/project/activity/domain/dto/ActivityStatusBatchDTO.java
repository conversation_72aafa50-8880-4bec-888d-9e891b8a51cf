package com.ruoyi.project.activity.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 活动状态批量修改DTO
 */
@Data
public class ActivityStatusBatchDTO {
    
    /**
     * 活动ID列表
     */
    @NotEmpty(message = "活动ID列表不能为空")
    private List<Long> ids;
    
    /**
     * 发布状态
     * 1: 发布
     * 0: 取消发布
     */
    @NotNull(message = "发布状态不能为空")
    private String status;
}
