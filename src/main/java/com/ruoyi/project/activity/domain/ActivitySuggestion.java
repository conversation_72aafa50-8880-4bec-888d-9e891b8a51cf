package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 意见建议实体类
 */
@Data
@TableName("activity_suggestion")
public class ActivitySuggestion extends BaseEntity {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联活动ID
     */
    @TableField("activity_id")
    private Long activityId;

    /**
     * 意见标题
     */
    @TableField("title")
    private String title;

    /**
     * 意见关键词
     */
    @TableField("keywords")
    private String keywords;

    /**
     * 意见内容
     */
    @TableField("content")
    private String content;

    /**
     * 发表人姓名
     */
    @TableField("submitter_name")
    private String submitterName;

    /**
     * 发表人ID
     */
    @TableField("submitter_id")
    private String submitterId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 审核状态(0-待审核,1-审核通过,2-审核不通过,3-暂存)
     */
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 审核人ID
     */
    @TableField("audit_user_id")
    private String auditUserId;

    /**
     * 审核人姓名
     */
    @TableField("audit_user_name")
    private String auditUserName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 状态(0-删除,1-正常)
     */
    @TableField("status")
    private String status;

}
