package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 更新活动通知DTO
 */
@Data
@ApiModel(value = "更新活动通知接收情况DTO")
public class ActivityNotificationReceptionUpdateDTO {

    /**
     * 活动通知接收情id
     */
    @ApiModelProperty(value = "id主键（单个更新必填，批量更新时忽略）")
    private String id;

    /**
     * 活动通知id
     */
    @ApiModelProperty(value = "活动通知id，更新单个通知的接收情况")
    private String notificationId;

    /** 活动id */
    @ApiModelProperty(value = "活动id更新某个活动所有通知的接收情况")
    private String activityId;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private Boolean isReaded;
}