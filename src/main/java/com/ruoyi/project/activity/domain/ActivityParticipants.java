package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动参与人员实体类
 */
@Data
//@EqualsAndHashCode(callSuper = true)
@TableName("activity_participants")
public class ActivityParticipants {
    
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 原UUID主键
     */
    @TableField("PKID")
    private String pkid;
    
    /**
     * 活动ID
     */
    @TableField("Activity_PKID")
    private String activityPkid;
    
    /**
     * 人员ID
     */
    @TableField("Pepole_PKID")
    private String pepolePkid;
    
    /**
     * 是否参加(1:是 0:否)
     */
    @TableField("Is_Attend")
    private String isAttend;
    
    /**
     * 不参加原因
     */
    @TableField("No_Attend_Reason")
    private String noAttendReason;
    
    /**
     * 创建人ID
     */
    @TableField("Create_ID")
    private String createId;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Create_Time")
    private Date createTime;
    
    /**
     * 更新人ID
     */
    @TableField("Update_ID")
    private String updateId;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Update_Time")
    private Date updateTime;
    
    /**
     * 是否负责人(1:是 0:否)
     */
    @TableField("is_leader")
    private String isLeader;
    
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
    
    /**
     * 区域ID
     */
    @TableField("region_id")
    private String regionId;
} 