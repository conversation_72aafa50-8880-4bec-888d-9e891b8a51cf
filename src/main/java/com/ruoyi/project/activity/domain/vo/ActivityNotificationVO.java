package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 活动通知VO
 */
@Data
@ApiModel(value = "活动通知VO")
public class ActivityNotificationVO {

    /**
     * 自增主键
     */
    @ApiModelProperty(value = "通知ID")
    private String id;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityId;

    /**
     * 活动标题
     */
    @ApiModelProperty(value = "活动标题")
    private String activityTitle;

    /**
     * 通知标题
     */
    @ApiModelProperty(value = "通知标题")
    private String title;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String content;

    /**
     * 是否发送短信
     */
    @ApiModelProperty(value = "是否发送短信")
    private Boolean isSendSms;

    /** 通知是否已发布 */
    @ApiModelProperty(value = "通知是否已发布")
    private Boolean isPublished;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createrName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createAt;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updateBy;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateAt;
}
