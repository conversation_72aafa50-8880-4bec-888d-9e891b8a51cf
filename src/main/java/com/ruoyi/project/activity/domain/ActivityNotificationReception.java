package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 活动通知接收情况实体类
 */
@Data
@TableName("activity_notification_reception")
public class ActivityNotificationReception {

    /**
     * id主键
     */
    @TableId("id")
    private Long id;

    /**
     * 通知ID
     */
    @TableField("notification_id")
    private Long notificationId;

    /**
     * 接收人ID
     */
    @TableField("recipient_id")
    private Long recipientId;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("receive_time")
    private Date receiveTime;

    /**
     * 接收状态
     */
    @TableField("receive_status")
    private Boolean receiveStatus;

    /**
     * 创建人ID
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_at")
    private Date createAt;

    /**
     * 更新人ID
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_at")
    private Date updateAt;

    /**
     * 逻辑删除标志位
     */
    // @TableLogic
    @TableField("del_flag")
    private Boolean delFlag;
}
