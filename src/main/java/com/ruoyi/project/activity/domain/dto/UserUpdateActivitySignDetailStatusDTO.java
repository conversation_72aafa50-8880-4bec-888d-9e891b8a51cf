package com.ruoyi.project.activity.domain.dto;

import com.ruoyi.common.enums.activity.SignStatusEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 当前用户活动签到明细查询DTO
 */
@Data
@ApiModel(value = "当前用户活动签到明细签到状态修改DTO")
public class UserUpdateActivitySignDetailStatusDTO {

    /**
     * 签到详情id
     */
    @ApiModelProperty(value = "签到详情id", required = true)
    private String signDetailId;

    /**
     * 签到状态
     * UNSIGNED: 未签到
     * SIGNED: 已签到
     * LEAVE: 请假
     */
    @ApiModelProperty(value = "签到状态", required = true)
    private SignStatusEnum signStatus;

    /**
     * 请假原因
     */
    @ApiModelProperty(value = "请假原因", required = false)
    private String reason;

}