package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 活动基本信息VO
 */
@Data
public class ActivityBasicinfoVO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 原UUID主键
     */
    private String pkid;

    /**
     * 活动主题
     */
    private String title;

    /**
     * 活动类型
     */
    private String type;

    /**
     * 活动地点
     */
    private String address;

    /**
     * 参与人数
     */
    private Integer participantCount;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityBeginDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndDate;

    /**
     * 活动城市
     */
    private String activityCity;

    /**
     * 发布状态
     */
    private String status;

    /**
     * 发起部门ID
     */
    private Long deptId;

    /**
     * 发起部门名称
     */
    private String deptName;

    /**
     * 当前用户是否已参加
     */
    private Boolean isJoined;
}
