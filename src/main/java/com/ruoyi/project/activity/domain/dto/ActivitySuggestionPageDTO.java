package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 意见建议分页查询DTO
 */
@Data
@ApiModel(value = "意见建议分页查询DTO")
public class ActivitySuggestionPageDTO {

    /**
     * 关联活动ID
     */
    @NotNull(message = "活动ID不能为空")
    @ApiModelProperty(value = "关联活动ID", required = true)
    private Long activityId;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer currentPage = 1;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;

    /**
     * 意见标题（模糊查询）
     */
    @ApiModelProperty(value = "意见标题（模糊查询）")
    private String title;

    /**
     * 创建时间范围-开始
     */
    @ApiModelProperty(value = "创建时间范围-开始", example = "2023-01-01 00:00:00")
    private String startTime;

    /**
     * 创建时间范围-结束
     */
    @ApiModelProperty(value = "创建时间范围-结束", example = "2023-12-31 23:59:59")
    private String endTime;
}
