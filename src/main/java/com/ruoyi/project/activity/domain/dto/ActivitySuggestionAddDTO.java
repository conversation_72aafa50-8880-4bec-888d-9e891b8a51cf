package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增意见建议DTO
 */
@Data
@ApiModel(value = "新增意见建议DTO")
public class ActivitySuggestionAddDTO {

    /**
     * 关联活动ID
     */
    @NotNull(message = "活动ID不能为空")
    @ApiModelProperty(value = "关联活动ID", required = true)
    private Long activityId;

    /**
     * 意见标题
     */
    @NotBlank(message = "意见标题不能为空")
    @ApiModelProperty(value = "意见标题", required = true)
    private String title;

    /**
     * 意见关键词
     */
    @ApiModelProperty(value = "意见关键词")
    @NotBlank(message = "意见关键词不能为空")
    private String keywords;

    /**
     * 意见内容
     */
    @NotBlank(message = "意见内容不能为空")
    @ApiModelProperty(value = "意见内容", required = true)
    private String content;

    /**
     * 发表人姓名
     */
    @NotBlank(message = "发表人姓名不能为空")
    @ApiModelProperty(value = "发表人姓名", required = true)
    private String submitterId;


    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "备注")
    private String remark;
}
