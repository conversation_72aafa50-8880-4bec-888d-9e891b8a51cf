package com.ruoyi.project.activity.domain.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.ruoyi.common.domain.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动通知分页查询DTO
 */
@Data
@ApiModel(value = "活动通知分页查询DTO")
public class ActivityNotificationPageDTO extends PageQuery {

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer currentPage = 1;

    // @ApiModelProperty(value = "每页记录数", example = "10")
    // private Long pageSize = 10L;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    @NotEmpty(message = "活动ID不能为空")
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityId;

    /**
     * 通知标题
     */
    @ApiModelProperty(value = "通知标题")
    private String title;

    /**
     * 发布人姓名（模糊查询）
     */
    @ApiModelProperty(value = "发布人姓名（模糊查询）")
    private String publisherName;

    /**
     * 通知是否已发布
     */
    @ApiModelProperty(value = "通知是否已发布")
    private Boolean isPublished;
}