package com.ruoyi.project.activity.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 新增活动签到DTO
 */
@Data
@ApiModel(value = "新增活动签到DTO")
public class ActivitySignAddDTO {

    /**
     * 活动ID
     */
    @NotBlank(message = "活动ID不能为空")
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityPkid;

    /**
     * 签到开始时间
     */
    @NotNull(message = "签到开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到开始时间", required = true)
    private Date signBeginDate;

    /**
     * 签到结束时间
     */
    @NotNull(message = "签到结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到结束时间", required = true)
    private Date signEndDate;

    /**
     * 签到说明
     */
    @ApiModelProperty(value = "签到说明")
    private String reason;
}
