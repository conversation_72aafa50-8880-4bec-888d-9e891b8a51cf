package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动报名分页查询DTO
 */
@Data
@ApiModel(value = "活动报名分页查询DTO")
public class ActivityRegistrationPageDTO {

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer currentPage = 1;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;

    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    private String title;

    /**
     * 活动类型
     */
    @ApiModelProperty(value = "活动类型")
    private String type;

    /**
     * 活动城市
     */
    @ApiModelProperty(value = "活动城市")
    private String activityCity;

    /**
     * 开始时间范围-开始
     */
    @ApiModelProperty(value = "开始时间范围-开始", example = "2023-01-01 00:00:00")
    private String beginDateStart;

    /**
     * 开始时间范围-结束
     */
    @ApiModelProperty(value = "开始时间范围-结束", example = "2023-12-31 23:59:59")
    private String beginDateEnd;
}
