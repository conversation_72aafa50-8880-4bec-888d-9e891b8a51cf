package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 活动签到明细VO
 */
@Data
@ApiModel(value = "活动签到明细VO")
public class ActivitySignDetailVO {

    /**
     * 自增主键
     */
    @ApiModelProperty(value = "自增主键")
    private Long id;

    /**
     * 原UUID主键
     */
    @ApiModelProperty(value = "原UUID主键")
    private String pkid;

    /**
     * 签到记录ID
     */
    @ApiModelProperty(value = "签到记录ID")
    private String signPkid;

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    private String pepolePkid;

    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String peopleName;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String unitPost;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date beginDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    /**
     * 本轮签到开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "本轮签到开始时间")
    private Date signBeginDate;

    /**
     * 本轮签到结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "本轮签到结束时间")
    private Date signEndDate;

    /**
     * 是否签到(1:是 0:否)
     */
    @ApiModelProperty(value = "是否签到(1:是 0:否)")
    private String isSign;

    /**
     * 签到状态文本
     */
    @ApiModelProperty(value = "签到状态文本")
    private String signStatusText;

    /**
     * 签到类型
     */
    @ApiModelProperty(value = "签到类型")
    private String signType;

    /**
     * 是否请假(1:是 0:否)
     */
    @ApiModelProperty(value = "是否请假(1:是 0:否)")
    private String isLeave;

    /**
     * 请假状态文本
     */
    @ApiModelProperty(value = "请假状态文本")
    private String leaveStatusText;

    /**
     * 原因说明
     */
    @ApiModelProperty(value = "原因说明")
    private String reason;

    /**
     * 是否负责人(1:是 0:否)
     */
    @ApiModelProperty(value = "是否负责人(1:是 0:否)")
    private String isLeader;
}
