package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 活动报名DTO
 */
@Data
@ApiModel(value = "活动报名DTO")
public class ActivityRegistrationDTO {

    /**
     * 活动ID
     */
    @NotBlank(message = "活动ID不能为空")
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityId;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    private String remark;
}
