package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 活动签到VO
 */
@Data
@ApiModel(value = "活动签到VO")
public class ActivitySignVO {

    /**
     * 自增主键
     */
    @ApiModelProperty(value = "自增主键")
    private Long id;

    /**
     * 原UUID主键
     */
    @ApiModelProperty(value = "原UUID主键")
    private String pkid;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityPkid;

    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    private String activityTitle;

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    private String pepolePkid;

    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String pepoleName;

    /**
     * 签到开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到开始时间")
    private Date signBeginDate;

    /**
     * 签到结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到结束时间")
    private Date signEndDate;

    /**
     * 原因说明
     */
    @ApiModelProperty(value = "原因说明")
    private String reason;

    @ApiModelProperty(value = "参加人员总数")
    private String totalPeople;

    @ApiModelProperty(value = "已签到人员数")
    private String signedPeople;

    @ApiModelProperty(value = "未签到人员数")
    private String unsignedPeople;
}
