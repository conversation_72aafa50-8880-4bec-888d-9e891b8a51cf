package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 活动参与人员不参加/请假DTO
 */
@Data
@ApiModel(value = "活动参与人员不参加/请假DTO")
public class ActivityParticipantAbsenceDTO {

    /**
     * 活动参与人员记录ID
     */
    @NotNull(message = "活动参与人员记录ID不能为空")
    @ApiModelProperty(value = "活动参与人员记录ID", required = true)
    private List<String> peoplePkids;

    /**
     * 活动ID
     */
    @NotBlank(message = "活动ID不能为空")
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityPkid;

    /**
     * 是否参加(1:参加 0:不参加 2:请假)
     */
    @NotBlank(message = "参加状态不能为空")
    @ApiModelProperty(value = "是否参加(1:参加 0:不参加 2:请假)", required = true)
    private String isAttend;

    /**
     * 不参加/请假原因
     */
    private String noAttendReason;
}
