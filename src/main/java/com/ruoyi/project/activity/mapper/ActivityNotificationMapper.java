package com.ruoyi.project.activity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.activity.domain.ActivityNotification;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationVO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动通知数据层
 */
@Mapper
public interface ActivityNotificationMapper extends BaseMapper<ActivityNotification> {

    /**
     * 分页查询活动通知列表
     *
     * @param page      分页参数
     * @param searchDto 查询参数
     * @return 活动通知分页列表
     */
    IPage<ActivityNotificationVO> selectNotificationPage(@Param("page") Page<ActivityNotificationVO> page,
            @Param("searchDto") ActivityNotificationPageDTO searchDto);

    /**
     * 根据ID查询活动通知详情
     *
     * @param id 通知ID
     * @return 活动通知详情
     */
    ActivityNotificationVO selectDetailById(@Param("id") String id);
}