package com.ruoyi.project.committee.evalrule.domain;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

import java.util.Date;

/**
 * 规则详情对象 rule_detail
 */
@Data
@TableName("rule_detail")
public class RuleDetail {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_UUID)
    private String pkid;

    /** 规则ID */
    private String rulePkid;

    /** 根节点ID */
    private String rootPkid;

    /** 是否根节点 */
    private String isRoot;

    /** 策略Key */
    private String strategyKey;

    /** 备注 */
    private String remark;

    /** 得分 */
    private String score;

    /** 排序 */
    private Integer sort;

    /** 关联数据 */
    private String associated;

    /** 最低分 */
    private String limitMinScore;

    /** 最高分 */
    private String limitMaxScore;

    /** 是否自主填报项 */
    private Boolean isDeclare;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 逻辑删除标志位 */
    @TableLogic
    private Boolean delFlag;

    /** 区域ID */
    private String regionId;
}
