package com.ruoyi.project.committee.evalrule.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.committee.evalrule.domain.RuleInfo;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoDto;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoEditDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;

import java.util.List;

/**
 * 规则信息 服务接口
 */
public interface IRuleInfoService extends IService<RuleInfo> {

    /**
     * 查询规则信息列表（树形结构）
     *
     * @return 规则信息树形列表
     */
    List<RuleInfoVo> selectRuleInfoList();

    /**
     * 根据ID查询规则信息（包含树形结构）
     *
     * @param pkid 规则信息ID
     * @return 规则信息树形结构
     */
    RuleInfoVo selectRuleInfoById(String pkid);

    /**
     * 根据年份查询规则信息（包含树形结构）
     *
     * @param year year
     * @return 规则信息树形结构
     */
    RuleInfoVo selectRuleInfoByYear(int  year);

    /**
     * 保存规则信息
     *
     * @param editDto 规则信息
     * @return 结果
     */
    String saveOrUpdateRule(RuleInfoEditDto editDto);

    /**
     * 修改规则信息
     *
     * @param editDto 规则信息
     * @return 结果
     */
    String updateRuleInfo(RuleInfoEditDto editDto);

    /**
     * 批量删除规则信息
     *
     * @param pkids 需要删除的规则信息ID数组
     * @return 结果
     */
    boolean deleteRuleInfoByIds(String[] pkids);

    /**
     * 删除规则信息信息
     *
     * @param pkid 规则信息ID
     * @return 结果
     */
    boolean deleteRuleInfoById(String pkid);

    /**
     * 生成新一年的规则信息（自动复用前一年的规则）
     *
     * @return 新生成的规则信息ID
     */
    String generateNewYearRule();
}
