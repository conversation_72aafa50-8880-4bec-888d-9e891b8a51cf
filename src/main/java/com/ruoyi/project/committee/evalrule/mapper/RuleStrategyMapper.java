package com.ruoyi.project.committee.evalrule.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.evalrule.domain.RuleStrategy;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RuleStrategyMapper extends BaseMapper<RuleStrategy> {

    default List<RuleStrategy> selectStrategyList() {
        return selectList(new LambdaQueryWrapper<RuleStrategy>()
                .eq(RuleStrategy::getIsEnabled, true)
        );
    }
}
