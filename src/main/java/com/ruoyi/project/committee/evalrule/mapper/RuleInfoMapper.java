package com.ruoyi.project.committee.evalrule.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.evalrule.domain.RuleInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 规则信息 Mapper 接口
 */
@Mapper
public interface RuleInfoMapper extends BaseMapper<RuleInfo> {

    default RuleInfo selectNewestRuleInfo() {
        return selectOne(new LambdaQueryWrapper<RuleInfo>()
                .orderByDesc(RuleInfo::getRuleYear)
                .last("limit 1")
        );
    }

    default RuleInfo getRuleInfoByYear(int year) {
        return selectOne(new LambdaQueryWrapper<RuleInfo>()
                .eq(RuleInfo::getRuleYear, year)
                .orderByDesc(RuleInfo::getCreateTime)
                .last("LIMIT 1")
        );
    };
}
