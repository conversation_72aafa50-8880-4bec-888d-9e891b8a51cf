package com.ruoyi.project.committee.evalrule.mapper;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RewardStrategyMapper {

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（特别突出）
     */
    Integer handleEnvProtectionSupervisionOutstanding(@Param("member") CommitteeMember member);

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（表现较好）
     */
    Integer handleEnvProtectionSupervisionGood(@Param("member") CommitteeMember member);

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（有所参与）
     */
    Integer handleEnvProtectionSupervisionParticipated(@Param("member") CommitteeMember member);

    /**
     * 参加脱贫攻坚与乡村振兴工作（特别突出）
     */
    Integer handleRuralRevitalizationOutstanding(@Param("member") CommitteeMember member);

    /**
     * 参加脱贫攻坚与乡村振兴工作（表现较好）
     */
    Integer handleRuralRevitalizationGood(@Param("member") CommitteeMember member);

    /**
     * 参加脱贫攻坚与乡村振兴工作（有所参与）
     */
    Integer handleRuralRevitalizationParticipated(@Param("member") CommitteeMember member);

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）
     */
    Integer handleGrassrootsDemocracyOutstanding(@Param("member") CommitteeMember member);

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）
     */
    Integer handleGrassrootsDemocracyGood(@Param("member") CommitteeMember member);

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）
     */
    Integer handleGrassrootsDemocracyParticipated(@Param("member") CommitteeMember member);

    /**
     * 参加书香政协学习、宣讲活动（特别突出）
     */
    Integer handleReadingActivitiesOutstanding(@Param("member") CommitteeMember member);

    /**
     * 参加书香政协学习、宣讲活动（表现较好）
     */
    Integer handleReadingActivitiesGood(@Param("member") CommitteeMember member);

    /**
     * 参加书香政协学习、宣讲活动（有所参与）
     */
    Integer handleReadingActivitiesParticipated(@Param("member") CommitteeMember member);

    /**
     * 参加新冠肺炎疫情防控（特别突出）
     */
    Integer handleEpidemicPreventionOutstanding(@Param("member") CommitteeMember member);

    /**
     * 参加新冠肺炎疫情防控（表现较好）
     */
    Integer handleEpidemicPreventionGood(@Param("member") CommitteeMember member);

    /**
     * 参加新冠肺炎疫情防控（有所参与）
     */
    Integer handleEpidemicPreventionParticipated(@Param("member") CommitteeMember member);

    /**
     * 助力项目建设服务，参加引进外资活动（特别突出）
     */
    Integer handleProjectServiceOutstanding(@Param("member") CommitteeMember member);

    /**
     * 助力项目建设服务，参加引进外资活动（表现较好）
     */
    Integer handleProjectServiceGood(@Param("member") CommitteeMember member);

    /**
     * 助力项目建设服务，参加引进外资活动（有所参与）
     */
    Integer handleProjectServiceParticipated(@Param("member") CommitteeMember member);

    /**
     * 助推创一流营商环境（特别突出）
     */
    Integer handleBusinessEnvironmentOutstanding(@Param("member") CommitteeMember member);

    /**
     * 助推创一流营商环境（表现较好）
     */
    Integer handleBusinessEnvironmentGood(@Param("member") CommitteeMember member);

    /**
     * 助推创一流营商环境（有所参与）
     */
    Integer handleBusinessEnvironmentParticipated(@Param("member") CommitteeMember member);

    /**
     * 完成区政协交办的其他任务（特别突出）
     */
    Integer handleOtherTasksOutstanding(@Param("member") CommitteeMember member);

    /**
     * 完成区政协交办的其他任务（表现较好）
     */
    Integer handleOtherTasksGood(@Param("member") CommitteeMember member);

    /**
     * 完成区政协交办的其他任务（有所参与）
     */
    Integer handleOtherTasksParticipated(@Param("member") CommitteeMember member);
    
}
