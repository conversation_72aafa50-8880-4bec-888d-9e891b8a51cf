package com.ruoyi.project.committee.evalrule.domain;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

import java.util.Date;

/**
 * 规则信息对象 rule_info
 */
@Data
@TableName("rule_info")
public class RuleInfo {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_UUID)
    private String pkid;

    /** 年份 */
    private String ruleYear;

    /** 是否可用 */
    private Boolean isEnable;

    /** 是否公开 */
    private Boolean isPublish;

    /** 备注 */
    private String remark;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 逻辑删除标志位 */
    @TableLogic
    private Boolean delFlag;

    /** 区域ID */
    private String regionId;
}
