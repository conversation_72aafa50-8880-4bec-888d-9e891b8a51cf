package com.ruoyi.project.committee.evalrule.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("rule_score")
public class RuleScore {

    @TableId
    private Long id;

    private Integer year;

    private Long userId;

    private String userName;

    private String numberId;

    private String unitPost;

    private Integer basicScore;

    private Integer rewardScore;

    private Integer totalScore;

    private String scoreDetail;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableLogic
    private Boolean delFlag;
}
