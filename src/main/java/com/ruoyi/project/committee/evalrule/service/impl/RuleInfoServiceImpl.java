package com.ruoyi.project.committee.evalrule.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constant;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.committee.evalrule.converter.RuleInfoConverter;
import com.ruoyi.project.committee.evalrule.domain.RuleDetail;
import com.ruoyi.project.committee.evalrule.domain.RuleInfo;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoDto;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoEditDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;
import com.ruoyi.project.committee.evalrule.mapper.RuleDetailMapper;
import com.ruoyi.project.committee.evalrule.mapper.RuleInfoMapper;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * 规则信息 服务实现类
 */
@Service
public class RuleInfoServiceImpl extends ServiceImpl<RuleInfoMapper, RuleInfo> implements IRuleInfoService {

    @Resource
    private RuleInfoMapper ruleInfoMapper;

    @Resource
    private RuleDetailMapper ruleDetailMapper;



    /**
     * 查询规则信息列表
     *
     * @return 规则信息列表
     */
    @Override
    public List<RuleInfoVo> selectRuleInfoList() {
        // 查询所有规则信息
        List<RuleInfo> ruleInfoList = ruleInfoMapper.selectList(null);
        if (ruleInfoList.isEmpty()) {
            return new ArrayList<>();
        }
        List<RuleInfoVo> ruleInfoVoList = RuleInfoConverter.INSTANCE.entityListToVoList(ruleInfoList);

        // 查询所有规则详情
        List<RuleDetail> allDetailList = ruleDetailMapper.selectList(new LambdaQueryWrapper<RuleDetail>()
                .orderByAsc(RuleDetail::getSort));
        if (allDetailList.isEmpty()) {
            return ruleInfoVoList;
        }

        Map<String, List<RuleDetail>> ruleDetailMap = allDetailList.stream()
                .collect(Collectors.groupingBy(RuleDetail::getRulePkid));

        Map<String, List<RuleDetail>> parentChildMap = allDetailList.stream()
                .filter(detail -> detail.getRootPkid() != null) // 只考虑有父节点的详情
                .collect(Collectors.groupingBy(RuleDetail::getRootPkid));


        for (RuleInfoVo ruleInfoVo : ruleInfoVoList) {
            List<RuleDetail> ruleDetails = ruleDetailMap.get(ruleInfoVo.getPkid());
            if (ObjectUtil.isEmpty(ruleDetails)) {
                continue;
            }

            List<RuleDetail> rootDetails = ruleDetails.stream()
                    .filter(detail -> "1".equals(detail.getIsRoot()))
                    .collect(Collectors.toList());

            // 4.3 转换为VO并构建树形结构
            List<RuleDetailVo> rootDetailVos = RuleInfoConverter.INSTANCE.detailListToVoList(rootDetails);
            buildChildrenTree(rootDetailVos, parentChildMap);

            // 4.4 设置子节点
            ruleInfoVo.setChildren(rootDetailVos);
        }

        return ruleInfoVoList;
    }

    /**
     * 递归构建子节点树
     *
     * @param detailList 详情列表
     * @param parentChildMap 父子节点映射关系
     */
    private void buildChildrenTree(List<RuleDetailVo> detailList, Map<String, List<RuleDetail>> parentChildMap) {
        if (ObjectUtil.isEmpty(detailList) || ObjectUtil.isEmpty(parentChildMap)) {
            return;
        }

        for (RuleDetailVo parentVo : detailList) {
            List<RuleDetail> children = parentChildMap.get(parentVo.getPkid());
            if (ObjectUtil.isNull(children)) {
                continue;
            }

            List<RuleDetailVo> childrenVos = RuleInfoConverter.INSTANCE.detailListToVoList(children);
            childrenVos.sort((a, b) -> a.getSort() == null ? -1 : (b.getSort() == null ? 1 : a.getSort()
                    .compareTo(b.getSort())));

            buildChildrenTree(childrenVos, parentChildMap);

            parentVo.setChildren(childrenVos);
        }
    }

    /**
     * 根据ID查询规则信息
     *
     * @param pkid 规则信息ID
     * @return 规则信息
     */
    @Override
    public RuleInfoVo selectRuleInfoById(String pkid) {
        // 1. 查询规则信息
        RuleInfo ruleInfo = ruleInfoMapper.selectById(pkid);
        if (ruleInfo == null) {
            return null;
        }

        RuleInfoVo ruleInfoVo = RuleInfoConverter.INSTANCE.entityToVo(ruleInfo);

        // 2. 查询所有相关的规则详情
        LambdaQueryWrapper<RuleDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.eq(RuleDetail::getRulePkid, ruleInfo.getPkid());
        detailQueryWrapper.orderByAsc(RuleDetail::getSort);
        List<RuleDetail> allDetails = ruleDetailMapper.selectList(detailQueryWrapper);

        if (allDetails.isEmpty()) {
            return ruleInfoVo; // 如果没有详情，直接返回规则信息
        }

        // 3. 过滤出根节点
        List<RuleDetail> rootDetails = allDetails.stream()
                .filter(detail -> "1".equals(detail.getIsRoot()))
                .collect(Collectors.toList());

        // 4. 构建父子节点映射关系
        Map<String, List<RuleDetail>> parentChildMap = allDetails.stream()
                .filter(detail -> detail.getRootPkid() != null) // 只考虑有父节点的详情
                .collect(Collectors.groupingBy(RuleDetail::getRootPkid));

        // 5. 转换为VO并构建树形结构
        List<RuleDetailVo> rootDetailVos = RuleInfoConverter.INSTANCE.detailListToVoList(rootDetails);
        buildChildrenTree(rootDetailVos, parentChildMap);

        // 6. 设置子节点
        ruleInfoVo.setChildren(rootDetailVos);
        return ruleInfoVo;
    }

    @Override
    public RuleInfoVo selectRuleInfoByYear(int year) {

        RuleInfo ruleInfoByYear = ruleInfoMapper.getRuleInfoByYear(year);
        if (ObjectUtil.isNull(ruleInfoByYear)) {
            return null;
        }

        return selectRuleInfoById(ruleInfoByYear.getPkid());
    }

    private void validateRuleInfo(String pkId) {
        if (ruleInfoMapper.selectById(pkId) == null) {
            throw new ServiceException("该规则信息不存在");
        }
    }

    private void validateRuleDetail(String pkId) {
        if (ruleDetailMapper.selectById(pkId) == null) {
            throw new ServiceException("该规则详情不存在");
        }
    }

    /**
     * 新增规则信息
     *
     * @param editDto 规则信息
     * @return 结果
     */
    @Override
    public String saveOrUpdateRule(RuleInfoEditDto editDto) {

        if (ObjectUtil.isEmpty(editDto.getPkid())) {
            if (ObjectUtil.isEmpty(editDto.getRulePkid())) {
                RuleInfo ruleInfo = RuleInfoConverter.INSTANCE.convertToRule(editDto);
                ruleInfo.setRuleYear(String.valueOf(DateUtil.thisYear()));
                ruleInfo.setIsEnable(true);
                ruleInfo.setIsPublish(true);

                ruleInfoMapper.insert(ruleInfo);
                return ruleInfo.getPkid();
            } else {
                RuleDetail ruleDetail = RuleInfoConverter.INSTANCE.convertToDetail(editDto);

                if (editDto.getIsRoot().equals("1")) {
                    if (ruleInfoMapper.selectById(editDto.getRootPkid()) == null) {
                        throw new ServiceException("父级节点不存在！");
                    }
                    ruleDetail.setRootPkid(null);
                } else {
                    RuleDetail validateResult = ruleDetailMapper.selectById(editDto.getRootPkid());
                    if (validateResult == null) {
                        throw new ServiceException("父级节点不存在！");
                    }
                }

                ruleDetailMapper.insert(ruleDetail);

                return ruleDetail.getPkid();
            }
        } else {
            return updateRuleInfo(editDto);
        }

    }

    /**
     * 修改规则信息
     *
     * @param editDto 规则信息
     * @return 结果
     */
    @Override
    public String updateRuleInfo(RuleInfoEditDto editDto) {
        if (ObjectUtil.isEmpty(editDto.getRulePkid())) {
            validateRuleInfo(editDto.getPkid());
            RuleInfo updateObj = RuleInfoConverter.INSTANCE.convertToRule(editDto);
            ruleInfoMapper.updateById(updateObj);
            return updateObj.getPkid();
        } else {
            validateRuleDetail(editDto.getPkid());
            RuleDetail updateObj = RuleInfoConverter.INSTANCE.convertToDetail(editDto);
            ruleDetailMapper.updateById(updateObj);
            return updateObj.getPkid();
        }
    }

    /**
     * 批量删除规则信息
     *
     * @param pkids 需要删除的规则信息ID数组
     * @return 结果
     */
    @Override
    public boolean deleteRuleInfoByIds(String[] pkids) {
        // 删除规则详情
        for (String pkid : pkids) {
            deleteRuleDetails(pkid);
        }

        // 删除规则信息
        return ruleInfoMapper.deleteBatchIds(Arrays.asList(pkids)) > 0;
    }

    /**
     * 删除规则信息信息
     *
     * @param pkid 规则信息ID
     * @return 结果
     */
    @Override
    public boolean deleteRuleInfoById(String pkid) {
        // 删除规则详情
        deleteRuleDetails(pkid);

        // 删除规则信息
        return ruleInfoMapper.deleteById(pkid) > 0;
    }

    /**
     * 删除规则详情
     *
     * @param rulePkid 规则ID
     */
    private void deleteRuleDetails(String rulePkid) {
        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleDetail::getRulePkid, rulePkid);
        ruleDetailMapper.delete(queryWrapper);
    }

    /**
     * 生成新一年的规则信息（自动复用前一年的规则）
     *
     * @return 新生成的规则信息ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateNewYearRule() {
        // 获取当前年份
        int currentYear = DateUtil.thisYear();
        String newYear = String.valueOf(currentYear);

        RuleInfo lastYearRule = ruleInfoMapper.selectNewestRuleInfo();
        if (lastYearRule == null) {
            throw new ServiceException("不存在委员履职考核规则，无法生成新规则");
        }

//        if (lastYearRule.getRuleYear().equals(newYear)) {
//            throw new ServiceException("当前年度的规则已存在");
//        }


        // 创建新年度的规则信息
        RuleInfo newYearRule = new RuleInfo();
        newYearRule.setRuleYear(newYear);
        newYearRule.setIsEnable(true);
        newYearRule.setIsPublish(true);
        newYearRule.setRemark(currentYear + "委员履职考核规则");

        // 保存新规则信息
        ruleInfoMapper.insert(newYearRule);

        // 查询上一年的所有规则详情
        LambdaQueryWrapper<RuleDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.eq(RuleDetail::getRulePkid, lastYearRule.getPkid());
        detailQueryWrapper.orderByAsc(RuleDetail::getSort);

        List<RuleDetail> lastYearDetails = ruleDetailMapper.selectList(detailQueryWrapper);

        // 构建新年度的规则详情
        if (lastYearDetails != null && !lastYearDetails.isEmpty()) {
            // 创建新旧ID映射关系，用于处理父子关系
            Map<String, String> oldToNewIdMap = new HashMap<>();

            // 第一次遍历，创建新的详情记录并建立映射关系
            for (RuleDetail oldDetail : lastYearDetails) {
                RuleDetail newDetail = copyOldDetailToNew(oldDetail, newYearRule);

                // 先保存新详情
                ruleDetailMapper.insert(newDetail);
                oldToNewIdMap.put(oldDetail.getPkid(), newDetail.getPkid());
            }

            // 第二次遍历，处理父子关系
            for (RuleDetail oldDetail : lastYearDetails) {
                if (oldDetail.getRootPkid() != null) {
                    // 获取新的详情ID
                    String newDetailId = oldToNewIdMap.get(oldDetail.getPkid());
                    // 获取新的父节点ID
                    String newRootId = oldToNewIdMap.get(oldDetail.getRootPkid());

                    if (newDetailId != null && newRootId != null) {
                        // 更新父子关系
                        RuleDetail updateDetail = new RuleDetail();
                        updateDetail.setPkid(newDetailId);
                        updateDetail.setRootPkid(newRootId);
                        ruleDetailMapper.updateById(updateDetail);
                    }
                }
            }
        }

        return newYearRule.getPkid();
    }

    private RuleDetail copyOldDetailToNew(RuleDetail oldDetail, RuleInfo newYearRule) {
        RuleDetail newDetail = new RuleDetail();
        newDetail.setRulePkid(newYearRule.getPkid());
        newDetail.setIsRoot(oldDetail.getIsRoot());
        newDetail.setStrategyKey(oldDetail.getStrategyKey());
        newDetail.setRemark(oldDetail.getRemark());
        newDetail.setScore(oldDetail.getScore());
        newDetail.setSort(oldDetail.getSort());
        newDetail.setAssociated(oldDetail.getAssociated());
        newDetail.setLimitMinScore(oldDetail.getLimitMinScore());
        newDetail.setLimitMaxScore(oldDetail.getLimitMaxScore());
        newDetail.setIsDeclare(oldDetail.getIsDeclare());
        return newDetail;
    }
}
