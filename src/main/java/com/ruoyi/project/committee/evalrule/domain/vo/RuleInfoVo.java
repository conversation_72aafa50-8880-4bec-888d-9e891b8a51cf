package com.ruoyi.project.committee.evalrule.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 规则信息VO
 */
@Data
@ApiModel(value = "规则信息VO", description = "规则信息视图对象")
public class RuleInfoVo {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String pkid;

    /** 年份 */
    @ApiModelProperty(value = "年份")
    private String ruleYear;

    /** 是否可用 */
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnable;

    /** 是否公开 */
    @ApiModelProperty(value = "是否公开")
    private Boolean isPublish;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 子规则列表 */
    @ApiModelProperty(value = "子规则列表")
    private List<RuleDetailVo> children;
}
