package com.ruoyi.project.committee.resumption.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.committee.resumption.domain.po.ExamineParticipant;
import com.ruoyi.project.committee.resumption.mapper.ExamineParticipantMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ExamineParticipantService extends ServiceImpl<ExamineParticipantMapper, ExamineParticipant> {

    @Resource
    private ExamineParticipantMapper examineParticipantMapper;

    @Transactional(rollbackFor = Exception.class)
    public void resetParticipant(String reportPkId, List<ExamineParticipant> participantList) {
        examineParticipantMapper.deleteByReportPkId(reportPkId);
        saveBatch(participantList);
    }
}
