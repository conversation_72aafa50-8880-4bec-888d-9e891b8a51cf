package com.ruoyi.project.committee.resumption.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.domain.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 公益情况分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("公益情况分页查询参数")
public class CommunityPageQuery extends PageQuery {

    private Integer currentPage = 1;

    @ApiModelProperty("届")
    private String electedPeriod;

    @ApiModelProperty("次")
    private String electedTimes;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("参与者名称")
    private String participantsName;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("专委会")
    private String committee;

    @ApiModelProperty("填报开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty("填报结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
