package com.ruoyi.project.committee.resumption.controller;

import com.ruoyi.common.domain.dto.PageDTO;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAddDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAuditDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorUpdateDTO;
import com.ruoyi.project.committee.resumption.domain.query.HonorPageQuery;
import com.ruoyi.project.committee.resumption.domain.query.HonorUserPageQuery;
import com.ruoyi.project.committee.resumption.domain.vo.HonorPageVo;
import com.ruoyi.project.committee.resumption.service.IHonorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "履职信息审核-获奖情况")
@RestController
@RequestMapping(value = "/honor")
@RequiredArgsConstructor
public class HonorController extends BaseController {

    private final IHonorService honorService;

    @ApiOperation("分页查询")
    @PostMapping("/getHonorList")
    public TableDataInfo getHonorList(@RequestBody HonorPageQuery query) {
         PageDTO<HonorPageVo> pageDTO = honorService.getHonorList(query);
         return getDataTable(pageDTO.getList(), pageDTO.getTotal());
    }

    @ApiOperation("获取获奖情况")
    @GetMapping("/detail/{id}")
    public AjaxResult getHonorDetail(@PathVariable("id") String id) {
        return success(honorService.getHonorDetail(id));
    }

    @ApiOperation("新增获奖情况")
    @Log(title = "新增获奖情况", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addHonor(@RequestBody HonorAddDTO honorAddDTO) {
        return honorService.addHonor(honorAddDTO);
    }

    @ApiOperation("修改获奖情况")
    @Log(title = "修改获奖情况", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult updateHonor(@RequestBody HonorUpdateDTO honorUpdateDTO) {
        return honorService.updateHonor(honorUpdateDTO);
    }

    @ApiOperation("审核获奖情况")
    @Log(title = "审核获奖情况", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult auditHonor(@RequestBody HonorAuditDTO honorAuditDTO) {
        return honorService.auditHonor(honorAuditDTO);
    }

    @ApiOperation("删除获奖情况")
    @Log(title = "删除获奖情况", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult deleteHonor(@RequestBody List<String> ids) {
        return honorService.deleteHonor(ids);
    }

    @ApiOperation("退回获奖情况")
    @Log(title = "退回获奖情况", businessType = BusinessType.UPDATE)
    @PostMapping("/return")
    public AjaxResult returnHonor(@RequestBody HonorAuditDTO honorAuditDTO) {
        return honorService.returnHonor(honorAuditDTO);
    }

    @ApiOperation("查询当前用户的获奖情况")
    @PostMapping("/getCurrentUserList")
    public TableDataInfo getCurrentUserList(@RequestBody HonorUserPageQuery query) {
        PageDTO<HonorPageVo> pageDTO = honorService.getCurrentUserHonorList(query);
        return getDataTable(pageDTO.getList(), pageDTO.getTotal());
    }
}