package com.ruoyi.project.committee.resumption.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.CommunityTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公益情况对象
 */
@Data
@TableName("t_community")
public class Community implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("pkid")
    private String pkid;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 采集时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    /**
     * 创建人
     */
    @TableField("create_name")
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_name")
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;

    /**
     * 发布人ID
     */
    @TableField("publish_id")
    private String publishId;

    /**
     * 社区类型
     */
    @TableField("community_type")
    private CommunityTypeEnum communityType;

    /**
     * 审核状态
     */
    @TableField("audi_status")
    private AuditStatusEnum audiStatus;

    /**
     * 参与者名称
     */
    @TableField("participants_name")
    private String participantsName;

    /**
     * 作者名称
     */
    @TableField("writer_name")
    private String writerName;

    /**
     * 是否启用
     */
    @TableField("is_enable")
    private String isEnable;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("check_time")
    private Date checkTime;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核人电话
     */
    @TableField("checker_phone")
    private String checkerPhone;

    /**
     * 审核原因
     */
    @TableField("check_reason")
    private String checkReason;
}
