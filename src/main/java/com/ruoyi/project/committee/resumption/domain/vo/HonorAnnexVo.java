package com.ruoyi.project.committee.resumption.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 荣誉附件视图对象
 */
@Data
public class HonorAnnexVo {

    @ApiModelProperty("附件ID")
    private Long id;

    @ApiModelProperty("荣誉ID")
    private String honorId;

    @ApiModelProperty("附件URL")
    private String url;

    @ApiModelProperty("附件名称")
    private String annexName;

    @ApiModelProperty("附件类型")
    private String annexType;
}
