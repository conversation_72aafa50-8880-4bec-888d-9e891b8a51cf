package com.ruoyi.project.committee.resumption.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公益情况详情视图对象
 */
@Data
@ApiModel("公益情况详情视图对象")
public class CommunityDetailVo {

    @ApiModelProperty("公益情况ID")
    private String id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("社区类型code")
    private String communityType;

    @ApiModelProperty("社区类型名称")
    private String communityTypeName;

    @ApiModelProperty("审核状态code")
    private String auditStatus;

    @ApiModelProperty("审核状态名称")
    private String auditStatusName;

    @ApiModelProperty("参与者名称")
    private String participantsName;

    @ApiModelProperty("作者名称")
    private String writerName;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @ApiModelProperty("审核人")
    private String checker;

    @ApiModelProperty("审核人电话")
    private String checkerPhone;

    @ApiModelProperty("审核原因")
    private String checkReason;

    @ApiModelProperty("附件列表")
    private List<CommunityAnnexVo> annexList;

    @ApiModelProperty("参与者列表")
    private List<ParticipantVo> participantList;
}
