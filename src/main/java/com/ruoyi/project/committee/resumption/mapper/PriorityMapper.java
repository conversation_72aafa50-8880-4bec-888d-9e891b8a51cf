package com.ruoyi.project.committee.resumption.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.honor.StatusEnum;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityAuditDto;
import com.ruoyi.project.committee.resumption.domain.po.Priority;
import org.apache.ibatis.annotations.Mapper;

/**
 * 重点工作信息 Mapper 接口
 */
@Mapper
public interface PriorityMapper extends BaseMapper<Priority> {
    default void auditPriority(PriorityAuditDto auditDto) {
        update(null, new LambdaUpdateWrapper<Priority>()
                .set(Priority::getChecker, auditDto.getChecker())
                .set(Priority::getCheckerPhone, auditDto.getCheckerPhone())
                .set(Priority::getCheckTime, auditDto.getCheckTime())
                .set(Priority::getCheckReason, auditDto.getCheckReason())
                .set(Priority::getAuditStatus, AuditStatusEnum.REVIEWED.getCode())
                .in(Priority::getId, auditDto.getIdList())
        );
    }

    default void returnPriority(PriorityAuditDto auditDto) {
        update(null, new LambdaUpdateWrapper<Priority>()
                .set(Priority::getChecker, auditDto.getChecker())
                .set(Priority::getCheckerPhone, auditDto.getCheckerPhone())
                .set(Priority::getCheckTime, auditDto.getCheckTime())
                .set(Priority::getCheckReason, auditDto.getCheckReason())
                .set(Priority::getAuditStatus, AuditStatusEnum.RETURNED.getCode())
                .in(Priority::getId, auditDto.getIdList())
        );
    };
}
