package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class PriorityPageDto {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "届")
    private String period;

    @ApiModelProperty(value = "次")
    private String rate;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "状态(0-暂存;1-提交)")
    private String status;

    @ApiModelProperty(value = "专委会")
    private String belongSpecialCommittee;

    @ApiModelProperty(value = "参与人")
    private String participant;

    @ApiModelProperty(value = "填报开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "填报结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
