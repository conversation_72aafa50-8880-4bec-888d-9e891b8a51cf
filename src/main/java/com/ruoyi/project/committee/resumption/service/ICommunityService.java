package com.ruoyi.project.committee.resumption.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.domain.dto.PageDTO;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAddDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAuditDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityUpdateDTO;
import com.ruoyi.project.committee.resumption.domain.po.Community;
import com.ruoyi.project.committee.resumption.domain.query.CommunityPageQuery;
import com.ruoyi.project.committee.resumption.domain.query.CommunityUserPageQuery;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityDetailVo;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityPageVo;

import java.util.List;

/**
 * 公益情况Service接口
 */
public interface ICommunityService extends IService<Community> {

    /**
     * 获取公益情况列表
     *
     * @param query 查询参数
     * @return 公益情况列表
     */
    PageDTO<CommunityPageVo> getCommunityList(CommunityPageQuery query);

    /**
     * 获取公益情况详情
     *
     * @param id 公益情况ID
     * @return 公益情况详情
     */
    CommunityDetailVo getCommunityDetail(String id);

    /**
     * 新增公益情况
     *
     * @param communityAddDTO 公益情况信息
     * @return 结果
     */
    AjaxResult addCommunity(CommunityAddDTO communityAddDTO);

    /**
     * 修改公益情况
     *
     * @param communityUpdateDTO 公益情况信息
     * @return 结果
     */
    AjaxResult updateCommunity(CommunityUpdateDTO communityUpdateDTO);

    /**
     * 审核公益情况
     *
     * @param communityAuditDTO 审核信息
     * @return 结果
     */
    AjaxResult auditCommunity(CommunityAuditDTO communityAuditDTO);

    /**
     * 删除公益情况，支持单个或多个ID
     *
     * @param ids 公益情况ID列表
     * @return 结果
     */
    AjaxResult deleteCommunity(List<String> ids);

    /**
     * 退回公益情况
     *
     * @param communityAuditDTO 审核信息
     * @return 结果
     */
    AjaxResult returnCommunity(CommunityAuditDTO communityAuditDTO);

    /**
     * 获取当前用户的公益情况列表
     * @param query 查询参数
     * @return 公益情况列表
     */
    PageDTO<CommunityPageVo> getCurrentUserCommunityList(CommunityUserPageQuery query);
}
