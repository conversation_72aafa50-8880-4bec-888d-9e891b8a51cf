package com.ruoyi.project.committee.resumption.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.domain.dto.PageDTO;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.committee.PersonTypeEnum;
import com.ruoyi.common.enums.committee.ReportTypeEnum;
import com.ruoyi.common.enums.honor.AwardTypeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.CollUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.committee.archive.domain.dto.CommitteeMemberQueryDTO;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo;
import com.ruoyi.project.committee.archive.service.ICommitteeMemberService;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAddDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAnnexDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAuditDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorUpdateDTO;
import com.ruoyi.project.committee.resumption.domain.po.ExamineParticipant;
import com.ruoyi.project.committee.resumption.domain.po.Honor;
import com.ruoyi.project.committee.resumption.domain.po.HonorAnnex;
import com.ruoyi.project.committee.resumption.domain.query.HonorPageQuery;
import com.ruoyi.project.committee.resumption.domain.query.HonorUserPageQuery;
import com.ruoyi.project.committee.resumption.domain.vo.HonorAnnexVo;
import com.ruoyi.project.committee.resumption.domain.vo.HonorDetailVo;
import com.ruoyi.project.committee.resumption.domain.vo.HonorPageVo;
import com.ruoyi.project.committee.resumption.domain.vo.ParticipantVo;
import com.ruoyi.project.committee.resumption.mapper.HonorMapper;
import com.ruoyi.project.committee.resumption.service.IHonorAnnexService;
import com.ruoyi.project.committee.resumption.service.IHonorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 获奖情况Service实现
 */
@Service
@RequiredArgsConstructor
public class HonorServiceImpl extends ServiceImpl<HonorMapper, Honor> implements IHonorService {

    private final IHonorAnnexService honorAnnexService;
    private final ICommitteeMemberService committeeMemberService;
    private final ExamineParticipantService  examineParticipantService;

    private boolean isParticipantInCommitteeMembers(String participantsName, List<String> committeeMembers) {
        if (StringUtils.isEmpty(participantsName) || CollUtils.isEmpty(committeeMembers)) {
            return false;
        }
        return java.util.Arrays.stream(participantsName.split(","))
                .map(String::trim)
                .anyMatch(committeeMembers::contains);
    }

    private HonorPageVo convertToPageVo(Honor honor) {
        HonorPageVo vo = new HonorPageVo();
        vo.setId(honor.getPkid());
        BeanUtil.copyProperties(honor, vo);

        if (honor.getAudiStatus() != null) {
            vo.setAuditStatus(honor.getAudiStatus().getLabel());
        }

        if (honor.getHonorType() != null) {
            vo.setHonorTypeName(honor.getHonorType().getLabel());
        }

        return vo;
    }

    private HonorAnnexVo convertToAnnexVo(HonorAnnex annex) {
        HonorAnnexVo annexVo = new HonorAnnexVo();
        BeanUtil.copyProperties(annex, annexVo);
        return annexVo;
    }

    private Date adjustEndTime(Date endTime) {
        if (endTime == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(endTime);
        if (cal.get(Calendar.HOUR_OF_DAY) == 0
                && cal.get(Calendar.MINUTE) == 0
                && cal.get(Calendar.SECOND) == 0
                && cal.get(Calendar.MILLISECOND) == 0) {
            cal.add(Calendar.DATE, 1);
            cal.add(Calendar.MILLISECOND, -1);
            return cal.getTime();
        }
        return endTime;
    }

    @Override
    public PageDTO<HonorPageVo> getHonorList(HonorPageQuery query) {
        query.setPageNo(query.getCurrentPage());
        Date endTime = adjustEndTime(query.getEndTime());

        Page<Honor> page = lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getTitle()), Honor::getTitle, query.getTitle())
                .like(StringUtils.isNotEmpty(query.getParticipantsName()), Honor::getParticipantsName, query.getParticipantsName())
                .eq(StringUtils.isNotEmpty(query.getAuditStatus()), Honor::getAudiStatus, AuditStatusEnum.fromLabel(query.getAuditStatus()))
                .ge(query.getStartTime() != null, Honor::getCreateTime, query.getStartTime())
                .le(endTime != null, Honor::getCreateTime, endTime)
                .eq(Honor::getIsEnable, "1")
                .page(query.toMpPageDefaultSortByCreateTimeDesc());

        List<Honor> records = page.getRecords();
        if (CollUtils.isEmpty(records)) {
            return PageDTO.empty(page);
        }

        // 如果有委员会、届期或次数参数，则需要查询委员信息
        List<String> committeeMembers = null;
        if (StringUtils.isNotEmpty(query.getCommittee()) || StringUtils.isNotEmpty(query.getElectedPeriod()) || StringUtils.isNotEmpty(query.getElectedTimes())) {
            // 创建查询DTO
            CommitteeMemberQueryDTO memberQueryDTO = new CommitteeMemberQueryDTO();
            memberQueryDTO.setBelongsSpecialCommittee(query.getCommittee());
            memberQueryDTO.setElectedPeriod(query.getElectedPeriod());
            memberQueryDTO.setElectedTimes(query.getElectedTimes());

            // 查询委员信息
            List<CommitteeMemberQueryVo> members = committeeMemberService.selectCommitteeMemberByCondition(memberQueryDTO);

            // 如果没有找到委员，返回空结果
            if (CollUtils.isEmpty(members)) {
                return PageDTO.<HonorPageVo>empty(0L, 0L);
            }

            // 提取委员姓名
            committeeMembers = members.stream()
                    .map(CommitteeMemberQueryVo::getUserName)
                    .collect(Collectors.toList());

            // 根据委员姓名过滤荣誉记录
            final List<String> finalCommitteeMembers = committeeMembers;
            List<HonorPageVo> filteredList = records.stream()
                    .filter(honor -> isParticipantInCommitteeMembers(honor.getParticipantsName(), finalCommitteeMembers))
                    .map(this::convertToPageVo)
                    .collect(Collectors.toList());

            // 返回过滤后的结果
            return new PageDTO<HonorPageVo>(Long.valueOf(filteredList.size()), Long.valueOf(Math.min(1, (filteredList.size() + query.getPageSize() - 1) / query.getPageSize())), filteredList);
        }

        // 如果没有届期、次数或委员会参数，返回所有记录
        List<HonorPageVo> voList = records.stream()
                .map(this::convertToPageVo)
                .collect(Collectors.toList());

        return PageDTO.<HonorPageVo>of(page, voList);
    }

    /**
     * 处理荣誉等级
     *
     * @param honor      Honor对象
     * @param honorLevel 荣誉等级字符串
     * @param isUpdate   是否是更新操作
     */
    private void processHonorLevel(Honor honor, String honorLevel, boolean isUpdate) {
        if (StringUtils.isEmpty(honorLevel)) {
            return;
        }

        if (isUpdate) {
            honor.setDistrictLevel(0);
            honor.setCityLevel(0);
            honor.setProvinceLevel(0);
            honor.setNationalLevel(0);
        }

        honor.setDistrictLevel(honorLevel.contains("区级") || honorLevel.contains("县级") || honorLevel.contains("区县级") ? 1 : 0);
        honor.setCityLevel(honorLevel.contains("市级") ? 1 : 0);
        honor.setProvinceLevel(honorLevel.contains("省级") ? 1 : 0);
        honor.setNationalLevel(honorLevel.contains("国家级") ? 1 : 0);
    }

    /**
     * 处理附件
     *
     * @param annexDTOList 附件DTO列表
     * @param honorId      荣誉ID
     * @param isUpdate     是否是更新操作
     * @return 处理结果，如果出错返回AjaxResult，否则返回null
     */
    private AjaxResult processAnnexes(List<HonorAnnexDTO> annexDTOList, String honorId, boolean isUpdate) {
        if (isUpdate && annexDTOList != null) {
            honorAnnexService.deleteByHonorId(honorId);
            if (CollUtils.isEmpty(annexDTOList)) {
                return null;
            }
        }

        if (CollUtils.isEmpty(annexDTOList)) {
            return null;
        }

        final String finalHonorId = honorId;
        List<HonorAnnex> annexList = annexDTOList.stream()
                .map(annexDTO -> {
                    HonorAnnex annex = new HonorAnnex();
                    BeanUtil.copyProperties(annexDTO, annex);
                    annex.setHonorId(finalHonorId);
                    annex.setCreateBy(SecurityUtils.getUsername());
                    annex.setCreateTime(new Date());
                    return annex;
                })
                .collect(Collectors.toList());

        return honorAnnexService.saveBatch(annexList) ? null : AjaxResult.error("保存附件信息失败");
    }

    @Override
    public HonorDetailVo getHonorDetail(String id) {
        Honor honor = getById(id);
        if (honor == null) {
            throw new ServiceException("荣誉信息不存在");
        }

        HonorDetailVo detailVo = new HonorDetailVo();
        BeanUtil.copyProperties(honor, detailVo);


        detailVo.setId(honor.getPkid());


        if (honor.getAudiStatus() != null) {
            detailVo.setAuditStatus(honor.getAudiStatus().getCode());
            detailVo.setAuditStatusName(honor.getAudiStatus().getLabel());
        }
        if (honor.getHonorType() != null) {
            detailVo.setHonorType(honor.getHonorType().getCode());
            detailVo.setHonorTypeName(honor.getHonorType().getLabel());
        }

//        StringBuilder honorLevel = new StringBuilder();
//        if (honor.getDistrictLevel() != null && honor.getDistrictLevel() == 1) {
//            honorLevel.append("区级");
//        }
//        if (honor.getCityLevel() != null && honor.getCityLevel() == 1) {
//            if (honorLevel.length() > 0) {
//                honorLevel.append(", ");
//            }
//            honorLevel.append("市级");
//        }
//        if (honor.getProvinceLevel() != null && honor.getProvinceLevel() == 1) {
//            if (honorLevel.length() > 0) {
//                honorLevel.append(", ");
//            }
//            honorLevel.append("省级");
//        }
//        if (honor.getNationalLevel() != null && honor.getNationalLevel() == 1) {
//            if (honorLevel.length() > 0) {
//                honorLevel.append(", ");
//            }
//            honorLevel.append("国家级");
//        }
//        detailVo.setHonorLevel(honorLevel.toString())
        detailVo.setDistrictLevel(honor.getDistrictLevel() == null ? 0 : honor.getDistrictLevel());
        detailVo.setCityLevel(honor.getCityLevel() == null ? 0 : honor.getCityLevel());
        detailVo.setProvinceLevel(honor.getProvinceLevel() == null ? 0 : honor.getProvinceLevel());
        detailVo.setNationalLevel(honor.getNationalLevel() == null ? 0 : honor.getNationalLevel());

        List<HonorAnnex> annexList = honorAnnexService.selectListByHonorId(id);
        if (!CollUtils.isEmpty(annexList)) {
            List<HonorAnnexVo> annexVoList = annexList.stream()
                    .map(this::convertToAnnexVo)
                    .collect(Collectors.toList());
            detailVo.setAnnexList(annexVoList);
        } else {
            detailVo.setAnnexList(Collections.emptyList());
        }

        List<ParticipantVo> participantList = examineParticipantService.getBaseMapper().selectParticipantByReportPkId(id);
        detailVo.setParticipantList(participantList);

        return detailVo;
    }

    private String generateId() {
        return System.currentTimeMillis() + "-" + String.format("%018d", (long) (Math.random() * 1000000000000000000L));
    }

    private AwardTypeEnum resolveHonorType(String typeCode) {
        if (StringUtils.isEmpty(typeCode)) {
            return AwardTypeEnum.OTHER;
        }
        AwardTypeEnum honorType = AwardTypeEnum.fromValue(typeCode);
        return honorType != null ? honorType : AwardTypeEnum.OTHER;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addHonor(HonorAddDTO honorAddDTO) {
        if (honorAddDTO == null) {
            return AjaxResult.error("参数不能为空");
        }

        Honor honor = new Honor();
        BeanUtil.copyProperties(honorAddDTO, honor, CopyOptions.create().setIgnoreProperties("honorType"));

        String id = generateId();
        honor.setPkid(id);
        honor.setCreateUser(SecurityUtils.getUsername());
        honor.setCreateTime(new Date());
        honor.setAudiStatus(AuditStatusEnum.UNREVIEWED);
        honor.setStatus("1");
        honor.setIsEnable("1");
        honor.setHonorType(resolveHonorType(honorAddDTO.getHonorType()));

//        processHonorLevel(honor, honorAddDTO.getHonorLevel(), false);

        if (!save(honor)) {
            return AjaxResult.error("保存荣誉信息失败");
        }

        List<ExamineParticipant> participantList = buildExamineParticipants(id, honorAddDTO.getParticipantList());
        examineParticipantService.saveBatch(participantList);

        AjaxResult annexResult = processAnnexes(honorAddDTO.getAnnexList(), id, false);
        if (annexResult != null) {
            return annexResult;
        }

        return AjaxResult.success("新增荣誉信息成功", id);
    }

    private void updateHonorFields(Honor honor, HonorUpdateDTO updateDTO) {
        if (StringUtils.isNotEmpty(updateDTO.getTitle())) {
            honor.setTitle(updateDTO.getTitle());
        }
        if (updateDTO.getPublishTime() != null) {
            honor.setPublishTime(updateDTO.getPublishTime());
        }
        if (StringUtils.isNotEmpty(updateDTO.getHonorType())) {
            honor.setHonorType(resolveHonorType(updateDTO.getHonorType()));
        }
        if (StringUtils.isNotEmpty(updateDTO.getRemark())) {
            honor.setRemark(updateDTO.getRemark());
        }
        if (StringUtils.isNotEmpty(updateDTO.getParticipantsName())) {
            honor.setParticipantsName(updateDTO.getParticipantsName());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateHonor(HonorUpdateDTO honorUpdateDTO) {
        if (honorUpdateDTO == null || StringUtils.isEmpty(honorUpdateDTO.getId())) {
            return AjaxResult.error("参数不能为空");
        }

        String id = honorUpdateDTO.getId();
        Honor honor = getById(id);
        if (honor == null) {
            return AjaxResult.error("荣誉信息不存在");
        }

        updateHonorFields(honor, honorUpdateDTO);
//        processHonorLevel(honor, honorUpdateDTO.getHonorLevel(), true);

        // 参与人
        List<ExamineParticipant> participantList = buildExamineParticipants(id, honorUpdateDTO.getParticipantList());
        examineParticipantService.resetParticipant(id, participantList);

        honor.setAudiStatus(AuditStatusEnum.UNREVIEWED);
        honor.setUpdateUser(SecurityUtils.getUsername());
        honor.setUpdateTime(new Date());

        if (!updateById(honor)) {
            return AjaxResult.error("更新荣誉信息失败");
        }

        AjaxResult annexResult = processAnnexes(honorUpdateDTO.getAnnexList(), id, true);
        if (annexResult != null) {
            return annexResult;
        }

        return AjaxResult.success("更新荣誉信息成功");
    }

    private AjaxResult processAuditOperation(HonorAuditDTO honorAuditDTO, AuditStatusEnum targetStatus, String operationType) {
        if (honorAuditDTO == null || CollUtils.isEmpty(honorAuditDTO.getIds())) {
            return AjaxResult.error("参数不能为空");
        }

        List<String> ids = honorAuditDTO.getIds();
        List<Honor> honorList = listByIds(ids);
        if (CollUtils.isEmpty(honorList)) {
            return AjaxResult.error("未找到相关荣誉信息");
        }

        List<Honor> pendingHonors = honorList.stream()
                .filter(honor -> honor.getAudiStatus() == AuditStatusEnum.UNREVIEWED)
                .collect(Collectors.toList());

        if (CollUtils.isEmpty(pendingHonors)) {
            return AjaxResult.error("没有可" + operationType + "的数据");
        }

        final Date now = new Date();
        final Date checkTime = honorAuditDTO.getCheckTime() != null ? honorAuditDTO.getCheckTime() : now;
        final String username = SecurityUtils.getUsername();

        pendingHonors.forEach(honor -> {
            honor.setAudiStatus(targetStatus);
            honor.setChecker(honorAuditDTO.getChecker());
            honor.setCheckerPhone(honorAuditDTO.getCheckerPhone());
            honor.setCheckTime(checkTime);
            honor.setCheckReason(honorAuditDTO.getCheckReason());
            honor.setUpdateUser(username);
            honor.setUpdateTime(now);
        });

        boolean updated = updateBatchById(pendingHonors);
        if (!updated) {
            return AjaxResult.error(operationType + "失败");
        }

        return AjaxResult.success(operationType + "成功，共" + operationType + pendingHonors.size() + "条数据");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult auditHonor(HonorAuditDTO honorAuditDTO) {
        return processAuditOperation(honorAuditDTO, AuditStatusEnum.REVIEWED, "审核");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult returnHonor(HonorAuditDTO honorAuditDTO) {
        return processAuditOperation(honorAuditDTO, AuditStatusEnum.RETURNED, "退回");
    }

    @Override
    public PageDTO<HonorPageVo> getCurrentUserHonorList(HonorUserPageQuery query) {
        query.setPageNo(query.getCurrentPage());
        Long userId = SecurityUtils.getUserId();
        Set<String> reportIdList = examineParticipantService.getBaseMapper().selectReportPkIdByCommittee(null, null, null, String.valueOf(userId), null);
        if (ObjectUtil.isEmpty(reportIdList)) {
            return PageDTO.empty(0L, 0L);
        }

        // 构建查询条件
        Page<Honor> page = lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getTitle()), Honor::getTitle, query.getTitle())
                .eq(StringUtils.isNotEmpty(query.getAuditStatus()), Honor::getAudiStatus, AuditStatusEnum.fromLabel(query.getAuditStatus()))
                .eq(StringUtils.isNotEmpty(query.getStatus()), Honor::getStatus, query.getStatus())
                .in(Honor::getPkid, reportIdList)
                .eq(Honor::getIsEnable, "1")
                .page(query.toMpPageDefaultSortByCreateTimeDesc());

        List<Honor> records = page.getRecords();
        if (CollUtils.isEmpty(records)) {
            return PageDTO.empty(page);
        }

        records.forEach(this::setHonorLevel);

        // 转换为VO对象
        List<HonorPageVo> voList = records.stream()
                .map(this::convertToPageVo)
                .collect(Collectors.toList());

        return PageDTO.of(page, voList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteHonor(List<String> ids) {
        if (CollUtils.isEmpty(ids)) {
            return AjaxResult.error("参数不能为空");
        }

        List<Honor> honorList = listByIds(ids);
        if (CollUtils.isEmpty(honorList)) {
            return AjaxResult.error("未找到相关荣誉信息");
        }

        // 设置逻辑删除标记
        final Date now = new Date();
        final String username = SecurityUtils.getUsername();

        honorList.forEach(honor -> {
            honor.setIsEnable("0"); // 设置为已删除
            honor.setUpdateUser(username); // 设置更新人，实际对应数据库中的update_name字段
            honor.setUpdateTime(now);
        });

        // 批量更新
        boolean updated = updateBatchById(honorList);
        if (!updated) {
            return AjaxResult.error("删除失败");
        }

        return AjaxResult.success("删除成功，共删除" + honorList.size() + "条数据");
    }

    private List<ExamineParticipant> buildExamineParticipants(String honorId, List<String> participantList) {
        return participantList.stream()
                .map(participant -> {
                    ExamineParticipant examineParticipant = new ExamineParticipant();
                    examineParticipant.setReportPkid(honorId);
                    examineParticipant.setCommitteePkid(participant);
                    examineParticipant.setReportType(ReportTypeEnum.AWARD);
                    examineParticipant.setPersonType(PersonTypeEnum.PARTICIPANT);
                    return examineParticipant;
                })
                .collect(Collectors.toList());
    }

    private void setHonorLevel(Honor honor) {

        if (ObjectUtil.isNotEmpty(honor.getHonorLevel())) {
            return;
        }

        StringBuilder honorLevel = new StringBuilder();
        if (honor.getDistrictLevel() != null && honor.getDistrictLevel() == 1) {
            honorLevel.append("区级");
        }
        if (honor.getCityLevel() != null && honor.getCityLevel() == 1) {
            if (honorLevel.length() > 0) {
                honorLevel.append(", ");
            }
            honorLevel.append("市级");
        }
        if (honor.getProvinceLevel() != null && honor.getProvinceLevel() == 1) {
            if (honorLevel.length() > 0) {
                honorLevel.append(", ");
            }
            honorLevel.append("省级");
        }
        if (honor.getNationalLevel() != null && honor.getNationalLevel() == 1) {
            if (honorLevel.length() > 0) {
                honorLevel.append(", ");
            }
            honorLevel.append("国家级");
        }
        honor.setHonorLevel(honorLevel.toString());
    }


}
