package com.ruoyi.project.committee.resumption.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class HonorPageVo {
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("参与人")
    private String participantsName;

    @ApiModelProperty("获奖类型名称")
    private String honorTypeName;

    @ApiModelProperty("获奖等级")
    private String honorLevel;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("填报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审核状态")
    private String auditStatus;
}