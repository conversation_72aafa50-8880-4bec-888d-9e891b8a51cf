package com.ruoyi.project.committee.resumption.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityAuditDto;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityEditDto;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityPageDto;
import com.ruoyi.project.committee.resumption.domain.po.Priority;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityPageVo;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityVo;

import java.util.List;

/**
 * 重点工作信息 服务接口
 */
public interface IPriorityService extends IService<Priority> {

    /**
     * 查询重点工作信息列表
     *
     * @param pageDto 重点工作信息查询参数
     * @return 重点工作信息列表
     */
    IPage<PriorityPageVo> selectPriorityPage(PriorityPageDto pageDto);

    /**
     * 查询我的重点工作信息列表
     *
     * @param pageDto 重点工作信息查询参数
     * @return 重点工作信息列表
     */
    IPage<PriorityPageVo> selectMyPriorityPage(PriorityPageDto pageDto);

    /**
     * 根据ID查询重点工作信息
     *
     * @param id 重点工作信息ID
     * @return 重点工作信息
     */
    PriorityVo selectPriorityById(String id);

    /**
     * 新增重点工作信息
     *
     * @param submitDto 重点工作信息
     * @return 结果
     */
    Long submitPriority(PriorityEditDto submitDto);

    /**
     * 修改重点工作信息
     *
     * @param editDto 重点工作信息
     * @return 结果
     */
    boolean updatePriority(PriorityEditDto editDto);

    /**
     * 批量删除重点工作信息
     *
     * @param idList 需要删除的重点工作信息ID数组
     * @return 结果
     */
    boolean deletePriorityByIds(List<String> idList);


    /**
     * 审核重点工作信息
     *
     * @param auditDto 重点工作信息
     * @return 结果
     */
    boolean auditPriority(PriorityAuditDto auditDto);

    /**
     * 退回重点工作信息
     * @param auditDto 重点工作信息
     * @return 结果
     */
    boolean returnPriority(PriorityAuditDto auditDto);
}
