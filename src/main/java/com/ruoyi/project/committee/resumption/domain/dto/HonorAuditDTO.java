package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 荣誉审核DTO
 */
@Data
@ApiModel("荣誉审核DTO")
public class HonorAuditDTO {

    @NotEmpty(message = "荣誉ID不能为空")
    @ApiModelProperty("荣誉ID列表，支持单个或多个ID")
    private List<String> ids;

    @NotBlank(message = "审核状态不能为空")
    @ApiModelProperty("审核人")
    private String checker;

    @NotBlank(message = "审核状态不能为空")
    @ApiModelProperty("联系电话")
    private String checkerPhone;

    @ApiModelProperty("时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @NotBlank(message = "审核理由不能为空")
    @ApiModelProperty("理由")
    private String checkReason;
}
