package com.ruoyi.project.committee.resumption.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.resumption.domain.po.HonorAnnex;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 荣誉附件Mapper接口
 */
@Mapper
public interface HonorAnnexMapper extends BaseMapper<HonorAnnex> {

    /**
     * 根据荣誉ID查询附件列表
     *
     * @param honorId 荣誉ID
     * @return 附件列表
     */
    default List<HonorAnnex> selectListByHonorId(String honorId) {
        LambdaQueryWrapper<HonorAnnex> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HonorAnnex::getHonorId, honorId);
        return selectList(queryWrapper);
    }

    /**
     * 根据荣誉ID删除附件
     *
     * @param honorId 荣誉ID
     */
    default void deleteByHonorId(String honorId) {
        LambdaQueryWrapper<HonorAnnex> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HonorAnnex::getHonorId, honorId);
        delete(queryWrapper);
    }
}
