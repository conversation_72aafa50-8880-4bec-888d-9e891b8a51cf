package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 重点工作信息DTO
 */
@Data
public class PriorityAuditDto {

    @NotEmpty(message = "id不能为空")
    @ApiModelProperty(value = "主键ID")
    private List<String> idList;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private Date checkTime;

    @ApiModelProperty(value = "审核人")
    private String checker;

    @ApiModelProperty(value = "审核人电话")
    private String checkerPhone;

    @ApiModelProperty(value = "审核原因")
    private String checkReason;

}
