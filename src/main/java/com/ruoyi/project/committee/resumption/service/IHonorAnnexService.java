package com.ruoyi.project.committee.resumption.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.committee.resumption.domain.po.HonorAnnex;

import java.util.List;

/**
 * 荣誉附件Service接口
 */
public interface IHonorAnnexService extends IService<HonorAnnex> {

    /**
     * 根据荣誉ID查询附件列表
     *
     * @param honorId 荣誉ID
     * @return 附件列表
     */
    List<HonorAnnex> selectListByHonorId(String honorId);

    /**
     * 根据荣誉ID删除附件
     *
     * @param honorId 荣誉ID
     */
    void deleteByHonorId(String honorId);

    /**
     * 批量保存附件
     *
     * @param annexList 附件列表
     * @return 结果
     */
    boolean saveBatch(List<HonorAnnex> annexList);
}
