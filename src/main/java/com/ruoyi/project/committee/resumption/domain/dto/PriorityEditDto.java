package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.committee.PriorityTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PriorityEditDto {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "标题")
    private String title;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private Date participationTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "填报时间")
    private Date collectTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "重点工作类型")
    private PriorityTypeEnum priorityType;

    @ApiModelProperty(value = "特别突出")
    private List<String> outstandingList;

    @ApiModelProperty(value = "表现良好")
    private List<String> goodList;

    @ApiModelProperty(value = "有所参与")
    private List<String> participantList;

    @ApiModelProperty(value = "附件列表")
    private List<String> annexList;

}
