package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 荣誉修改DTO
 */
@Data
@ApiModel("荣誉修改DTO")
public class HonorUpdateDTO {

    @ApiModelProperty("荣誉ID")
    private String id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("获奖时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("获奖类型")
    private String honorType;

    @ApiModelProperty("获奖等级")
    private String honorLevel;

    @ApiModelProperty("区县级")
    private Integer districtLevel = 0;

    @ApiModelProperty("市级")
    private Integer cityLevel = 0;

    @ApiModelProperty("省级")
    private Integer provinceLevel = 0;

    @ApiModelProperty("国家级")
    private Integer nationalLevel = 0;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("参与人")
    private String participantsName;

    @ApiModelProperty("参与人列表")
    private List<String> participantList;

    @ApiModelProperty("附件列表")
    private List<HonorAnnexDTO> annexList;
}
