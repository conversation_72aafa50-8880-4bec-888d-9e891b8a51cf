package com.ruoyi.project.committee.resumption.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.enums.committee.PersonTypeEnum;
import com.ruoyi.common.enums.committee.ReportTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_examine_participants")
public class ExamineParticipant {

    /**主键id*/
    @TableId(type = IdType.ASSIGN_UUID)
    private String pkid;

    /**Unknown Id*/
    private String reportPkid;

    /** 委员id */
    private String committeePkid;

    private ReportTypeEnum reportType;

    private PersonTypeEnum personType;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableLogic
    private Boolean delFlag;
}
