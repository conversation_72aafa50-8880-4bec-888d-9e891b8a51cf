package com.ruoyi.project.committee.meeting.converter;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.meeting.domain.MeetingDoc;
import com.ruoyi.project.committee.meeting.domain.MeetingInfo;
import com.ruoyi.project.committee.meeting.domain.MeetingSign;
import com.ruoyi.project.committee.meeting.domain.MeetingSignDetail;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingDocEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignEditDto;
import com.ruoyi.project.committee.meeting.domain.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MeetingConverter {

    MeetingConverter INSTANCE = Mappers.getMapper(MeetingConverter.class);

    MeetingInfo convert(MeetingEditDto meetingEditDto);

    MeetingInfoVo convertToVo(MeetingInfo meetingInfo);

    @Mapping(target = "meetingTime", source = "meetingInfo", qualifiedByName = "formatMeetingTime")
    MeetingInfoPageVo convert(MeetingInfo meetingInfo);

    Page<MeetingInfoPageVo> convert(IPage<MeetingInfo> page);

    // 会议签到信息
    MeetingSign convert(MeetingSignEditDto meetingSignEditDto);

    Page<MeetingSignPageVo> convertToSignPage(IPage<MeetingSign> page);

    // 会议签到详情
    List<MeetingSignDetailVo> convert(List<MeetingSignDetail> list);

    MeetingDoc convert(MeetingDocEditDto meetingDocEditDto);

    MeetingDocVo  convert(MeetingDoc meetingDoc);

    @Mapping(target = "docType", expression = "java(meetingDoc.getDocType().getDescription())")
    MeetingDocPageVo convertToPageVo(MeetingDoc meetingDoc);

    Page<MeetingDocPageVo> convertToDocPage(IPage<MeetingDoc> page);

    @Named("formatMeetingTime")
    static String formatMeetingTime(MeetingInfo meetingInfo) {
        String startTime = DateUtil.format(meetingInfo.getStartTime(), "yyyy-MM-dd HH:mm");
        String endTime = DateUtil.format(meetingInfo.getEndTime(), "yyyy-MM-dd HH:mm");
        return startTime + "~" + endTime;
    }
}
