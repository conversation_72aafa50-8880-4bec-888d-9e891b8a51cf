package com.ruoyi.project.committee.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeetingParticipantSearchDto {

    @NotBlank
    @ApiModelProperty(value = "会议id")
    private String meetingId;

    @ApiModelProperty(value = "委员姓名")
    private String peopleName;
}