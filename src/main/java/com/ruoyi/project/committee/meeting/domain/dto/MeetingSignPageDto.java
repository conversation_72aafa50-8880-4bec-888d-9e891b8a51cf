package com.ruoyi.project.committee.meeting.domain.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
public class MeetingSignPageDto {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    @NotBlank
    @ApiModelProperty(value = "会议id")
    private String meetingId;

    @ApiModelProperty(value = "签到说明")
    private String reason;
}
