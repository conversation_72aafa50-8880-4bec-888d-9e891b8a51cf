package com.ruoyi.project.committee.meeting.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 会议签到信息对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_sign")
public class MeetingSign extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 参与委员id
     */
    private Long peopleId;

    /**
     * 开始时间
     */
    private Date signBeginTime;

    /**
     * 结束时间
     */
    private Date signEndTime;

    /**
     * 是否签到
     */
    private Boolean isSign;

    /**
     * 签到类型
     */
    private String signType;

    /**
     * 是否请假
     */
    private String isLeave;

    /**
     * 签到说明
     */
    private String reason;

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 逻辑删除标志位
     */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}
