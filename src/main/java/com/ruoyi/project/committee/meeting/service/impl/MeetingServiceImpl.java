package com.ruoyi.project.committee.meeting.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.meeting.converter.MeetingConverter;
import com.ruoyi.project.committee.meeting.domain.MeetingInfo;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingPageDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingParticipantSearchDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingParticipantsVo;
import com.ruoyi.project.committee.meeting.mapper.MeetingMapper;
import com.ruoyi.project.committee.meeting.service.IMeetingParticipantsService;
import com.ruoyi.project.committee.meeting.service.IMeetingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.ObjectUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;


/**
 * 会议管理Service业务层处理
 */
@Slf4j
@Service
public class MeetingServiceImpl implements IMeetingService {

    @Resource
    private MeetingMapper meetingMapper;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    @Resource
    private IMeetingParticipantsService meetingParticipantsService;

    /**
     * 校验会议信息是否存在
     *
     * @param id 会议ID
     */
    private void validate(String id) {
        MeetingInfo meetingInfo = meetingMapper.selectById(id);
        if (ObjectUtil.isNull(meetingInfo)) {
            throw new ServiceException("会议信息不存在");
        }
    }

    @Override
    public IPage<MeetingInfoPageVo> getMeetingPage(MeetingPageDto pageDto) {
        Page<MeetingInfo> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        IPage<MeetingInfo> pageResult = meetingMapper.getMeetingPage(page, pageDto);

        return MeetingConverter.INSTANCE.convert(pageResult);
    }

    @Override
    public IPage<MeetingInfoPageVo> getMyPage(MeetingPageDto pageDto) {
        Long currentUserId = null;
        try {
            currentUserId  = SecurityUtils.getUserId();
        } catch (Exception e) {
            return PageUtils.emptyPage();
        }

        Page<MeetingInfo> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        IPage<MeetingInfoPageVo> pageResult = meetingMapper.getMyMeetingPage(page, pageDto, currentUserId);

        pageResult.getRecords().forEach(pageVo -> {
            String startTime = DateUtil.format(pageVo.getStartTime(), "yyyy-MM-dd HH:mm");
            String endTime = DateUtil.format(pageVo.getEndTime(), "yyyy-MM-dd HH:mm");
            pageVo.setMeetingTime(startTime + "~" + endTime);
        });

        return pageResult;
    }

    @Override
    public MeetingInfoVo getMeetingById(String id) {
        MeetingInfo meetingInfo = meetingMapper.selectById(id);
        if (ObjectUtil.isNull(meetingInfo)) {
            throw new ServiceException("会议信息不存在");
        }

        MeetingInfoVo meetingInfoVo = MeetingConverter.INSTANCE.convertToVo(meetingInfo);
        String meetingParticipants = meetingParticipantsService.getMeetingParticipants(id);
        meetingInfoVo.setMembers(meetingParticipants);
        return meetingInfoVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMeetingInfo(MeetingEditDto meetingEditDto) {
        MeetingInfo meetingInfo = MeetingConverter.INSTANCE.convert(meetingEditDto);
        meetingMapper.insert(meetingInfo);
        // 会议参加委员
        meetingParticipantsService.createParticipants(meetingInfo.getId(), meetingEditDto.getMemberList());
        return meetingInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMeetingInfo(MeetingEditDto meetingEditDto) {
        // 校验会议是否存在
        MeetingInfo meetingInfo = meetingMapper.selectById(meetingEditDto.getId());
        if (ObjectUtil.isNull(meetingInfo)) {
            throw new ServiceException("会议信息不存在");
        } else {
            if (meetingInfo.getIsPublish()) {
                throw new ServiceException("会议已发布，无法修改");
            }
        }

        MeetingInfo updateObj = MeetingConverter.INSTANCE.convert(meetingEditDto);
        meetingParticipantsService.resetParticipants(meetingInfo.getId(), meetingEditDto.getMemberList());
        return meetingMapper.updateById(updateObj) > 0;
    }

    @Override
    public Boolean deleteMeetingInfo(String id) {
        // 校验会议是否存在
        validate(id);

        return meetingMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean publish(List<String> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return true;
        }
        Integer affectRows = meetingMapper.changePublishStatus(idList, true);
        return affectRows > 0;
    }

    @Override
    public Boolean unPublish(List<String> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return true;
        }
        Integer affectRows = meetingMapper.changePublishStatus(idList, false);
        return affectRows > 0;
    }

    @Override
    public void exportParticipants(String id, HttpServletResponse response) {
        try {
            String fileName = "会议参加情况";
            List<MeetingParticipantsVo> participantsList = meetingParticipantsService.getMeetingParticipantsList(new MeetingParticipantSearchDto(id, null));

            ExcelUtil<MeetingParticipantsVo> util = new ExcelUtil<>(MeetingParticipantsVo.class);
            util.exportExcel(response, participantsList, "会议参加情况");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));
        } catch (Exception e) {
            log.info("导出会议参加情况失败！ >> {}", e.getMessage());
        }

    }

}
