package com.ruoyi.project.committee.meeting.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MeetingSignVo {

    private String id;

    @ApiModelProperty(value = "会议id")
    private Long meetingId;

    @ApiModelProperty(value = "参与委员id")
    private Long peopleId;

    @ApiModelProperty(value = "委员姓名")
    private String peopleName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date signBeginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date signEndTime;

    @ApiModelProperty(value = "是否签到")
    private Boolean isSign;

    @ApiModelProperty(value = "签到类型")
    private String signType;

    @ApiModelProperty(value = "是否请假")
    private String isLeave;

    @ApiModelProperty(value = "请假理由")
    private String reason;
}
