package com.ruoyi.project.committee.meeting.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.committee.MeetingTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MeetingSignEditDto {

    @ApiModelProperty(value = "会议id")
    private String meetingId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date signBeginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date signEndTime;

    @ApiModelProperty(value = "签到说明")
    private String reason;
}
