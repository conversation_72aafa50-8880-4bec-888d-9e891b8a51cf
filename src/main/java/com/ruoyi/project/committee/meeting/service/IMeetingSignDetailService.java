package com.ruoyi.project.committee.meeting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.committee.meeting.domain.MeetingSignDetail;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignOperateDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignDetailVo;

import java.util.List;

/**
 * 会议签到详情 Service接口
 */
public interface IMeetingSignDetailService {

    /**
     * 获取会议签到详情列表
     *
     * @param signId 查询参数
     * @return 签到详情数据
     */
    List<MeetingSignDetailVo> getMeetingSignDetailList(String signId);

    /**
     * 批量签到
     *
     * @param operationDto operationDto
     * @return 批量签到结果
     */
    Boolean batchSignIn(MeetingSignOperateDto operationDto); 

    /**
     * 签到
     *
     * @param signDetailId signDetailId
     * @return 成功签到的数量
     */
    Boolean signIn(String signDetailId);

    /**
     * 请假
     *
     * @param operationDto operationDto
     * @return 是否成功
     */
    Boolean leave(MeetingSignOperateDto operationDto);

    /**
     * 取消签到或请假
     *
     * @param operationDto operationDto
     * @return 是否成功
     */
    Boolean cancel(MeetingSignOperateDto operationDto);

    /**
     * 批量保存会议签到详情
     * @param signDetailList signDetailList
     */
    void saveBatchSignDetail(List<MeetingSignDetail> signDetailList);
}
