package com.ruoyi.project.committee.meeting.domain.dto;

import com.ruoyi.common.enums.committee.DocTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MeetingDocPageDto {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    private String meetingId;

    @ApiModelProperty(value = "会议名称")
    private String title;

    @ApiModelProperty(value = "是否公开")
    private Boolean isPublicly;

    @ApiModelProperty(value = "会议类型")
    private DocTypeEnum docType;


}
