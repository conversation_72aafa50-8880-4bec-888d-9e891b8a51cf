package com.ruoyi.project.committee.archive.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberStatisticsPageDto {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    @ApiModelProperty(value = "届")
    private String period;

    @ApiModelProperty(value = "次")
    private String rate;

    @ApiModelProperty(value = "委员姓名")
    private String memberName;

    @ApiModelProperty(value = "委员编号")
    private String numberId;

    @ApiModelProperty(value = "所属界别")
    private String sector;

    @ApiModelProperty(value = "专委会")
    private String specialCommittee;

    @ApiModelProperty(value = "委员性别")
    private String memberSex;

    @ApiModelProperty(value = "是否常委")
    private Boolean isScm;
}
