package com.ruoyi.project.committee.archive.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


@Mapper
public interface CommitteeMemberMapper extends BaseMapper<CommitteeMember> {

    CommitteeMember getMemberInfo(String id);

    default CommitteeMember getMemberInfoByUserId(String userId) {
        return selectOne(new LambdaQueryWrapper<CommitteeMember>()
                .eq(CommitteeMember::getUserId, userId)
                .orderByDesc(CommitteeMember::getYear)
                .last("LIMIT 1"));
    };

    default List<CommitteeMember> selectMemberByYear(Integer year) {
        return selectList(new LambdaQueryWrapper<CommitteeMember>()
                .select(
                        CommitteeMember::getId,
                        CommitteeMember::getYear,
                        CommitteeMember::getUserId,
                        CommitteeMember::getUserName,
                        CommitteeMember::getNumberId,
                        CommitteeMember::getUnitPost
                )
                .eq(CommitteeMember::getYear, year));
    }

    Set<Long> getMemberIdsByUserId(Long userId);

    /**
     * 根据条件查询委员信息
     * @param electedPeriod 当选届次
     * @param electedTimes 当选次数
     * @param userName 委员姓名
     * @param sector 界别
     * @param belongsSpecialCommittee 所属专委会
     * @param year 年份，如果为空则查询全部数据
     * @return 委员信息列表
     */
    List<CommitteeMemberQueryVo> selectCommitteeMemberByCondition(
            @Param("electedPeriod") String electedPeriod,
            @Param("electedTimes") String electedTimes,
            @Param("userName") String userName,
            @Param("sector") String sector,
            @Param("belongsSpecialCommittee") String belongsSpecialCommittee,
            @Param("year") Integer year);

}
