package com.ruoyi.project.committee.archive.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsEditVo;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsPageDto;
import com.ruoyi.project.committee.archive.domain.vo.MemberCountVo;
import com.ruoyi.project.committee.archive.domain.vo.MemberStatisticsVo;

public interface IMemberStatisticsService {

    IPage<MemberStatisticsVo> selectMemberStatisticsPage(MemberStatisticsPageDto pageDto);

    Long add(MemberStatisticsEditVo editVo);

    Boolean edit(MemberStatisticsEditVo editVo);

    Boolean delete(String id);

    MemberCountVo getById(String id);
}
