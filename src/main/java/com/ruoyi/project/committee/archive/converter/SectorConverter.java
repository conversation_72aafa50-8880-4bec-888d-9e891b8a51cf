package com.ruoyi.project.committee.archive.converter;

import com.ruoyi.project.committee.archive.domain.Sector;
import com.ruoyi.project.committee.archive.domain.vo.SectorVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SectorConverter {

    SectorConverter INSTANCE = Mappers.getMapper(SectorConverter.class);

    SectorVo convertTo(Sector sector);

    List<SectorVo> convertTo(List<Sector> sectorList);
}
