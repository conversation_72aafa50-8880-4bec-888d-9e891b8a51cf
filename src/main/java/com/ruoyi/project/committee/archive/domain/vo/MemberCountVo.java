package com.ruoyi.project.committee.archive.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberCountVo {

    @ApiModelProperty(value = "会议信息")
    private Integer meetingNum = 0;

    @ApiModelProperty(value = "履职报告")
    private Integer reportNum = 0;

    @ApiModelProperty(value = "议政协商")
    private Integer chatNum = 0;

    @ApiModelProperty(value = "活动信息")
    private Integer activityNum = 0;

    @ApiModelProperty(value = "委员提案")
    private Integer proposalNum = 0;

    @ApiModelProperty(value = "社情民意")
    private Integer opinionsNum = 0;

}
