package com.ruoyi.project.committee.archive.converter;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CommitteeMemberConverter {

    CommitteeMemberConverter INSTANCE = Mappers.getMapper(CommitteeMemberConverter.class);

    CommitteeMemberVo convertTo(CommitteeMember bean);
}
