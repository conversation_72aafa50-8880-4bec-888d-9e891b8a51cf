package com.ruoyi.project.committee.archive.service.impl;

import com.ruoyi.project.committee.archive.domain.ArchiveDept;
import com.ruoyi.project.committee.archive.mapper.ArchiveDeptMapper;
import com.ruoyi.project.committee.archive.service.IArchiveDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 部门管理 服务实现
 */
@Service
public class ArchiveDeptServiceImpl implements IArchiveDeptService {
    @Autowired
    private ArchiveDeptMapper deptMapper;

    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<ArchiveDept> selectDeptList(ArchiveDept dept) {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 根据祖级列表查询部门
     * 
     * @param ancestors 祖级列表
     * @return 部门列表
     */
    @Override
    public List<ArchiveDept> selectDeptListByAncestors(String ancestors) {
        return deptMapper.selectDeptListByAncestors(ancestors);
    }

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public ArchiveDept selectDeptById(Long deptId) {
        return deptMapper.selectDeptById(deptId);
    }
}
