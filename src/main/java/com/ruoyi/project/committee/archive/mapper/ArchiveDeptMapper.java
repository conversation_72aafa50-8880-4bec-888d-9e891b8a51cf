package com.ruoyi.project.committee.archive.mapper;

import com.ruoyi.project.committee.archive.domain.ArchiveDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门管理 数据层
 */
@Mapper
public interface ArchiveDeptMapper {
    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<ArchiveDept> selectDeptList(ArchiveDept dept);

    /**
     * 根据祖级列表查询部门
     * 
     * @param ancestors 祖级列表
     * @return 部门列表
     */
    public List<ArchiveDept> selectDeptListByAncestors(@Param("ancestors") String ancestors);

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    public ArchiveDept selectDeptById(Long deptId);
}
