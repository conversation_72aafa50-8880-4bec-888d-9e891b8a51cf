package com.ruoyi.project.committee.archive.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.enums.committee.SectorType;
import com.ruoyi.project.committee.archive.domain.Sector;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SectorMapper extends BaseMapper<Sector> {

    default List<Sector> selectSectorList(String sectorName, SectorType sectorType) {
        return selectList(new LambdaQueryWrapper<Sector>()
                .like(ObjectUtil.isNotEmpty(sectorName), Sector::getSectorName, sectorName)
                .eq(ObjectUtil.isNotEmpty(sectorType), Sector::getSectorType, sectorType)
                .orderByAsc(Sector::getSort));
    }
}
