package com.ruoyi.project.committee.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 委员查询条件DTO
 */
@Data
@ApiModel(value = "委员查询条件")
public class CommitteeMemberQueryDTO {

    @ApiModelProperty(value = "当选届期")
    private String electedPeriod;

    @ApiModelProperty(value = "当选次数")
    private String electedTimes;

    @ApiModelProperty(value = "委员姓名")
    private String userName;

    @ApiModelProperty(value = "界别")
    private String sector;

    @ApiModelProperty(value = "所属专委会")
    private String belongsSpecialCommittee;

    @ApiModelProperty(value = "年份，格式：yyyy，如果为空则查询全部数据")
    private Integer year;
}
