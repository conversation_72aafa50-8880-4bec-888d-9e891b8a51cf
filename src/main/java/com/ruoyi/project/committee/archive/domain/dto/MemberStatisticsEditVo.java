package com.ruoyi.project.committee.archive.domain.dto;

import lombok.Data;

@Data
public class MemberStatisticsEditVo {

    private Long id;

    /** 用户id */
    private Long userId;

    /** 全会 */
    private Integer generalMeeting;

    /** 常委会 */
    private Integer standingCommittee;

    /** 各类协商会 */
    private Integer consultativeConferences;

    /** 其他会议 */
    private Integer otherMeetings;

    /** 委员提案 */
    private Integer proposals;

    /** 大会发言 */
    private Integer confSpeech;

    /** 社情民意数量 */
    private Integer opinions;

    /** 参加活动 */
    private Integer joinActivity;

    /** 调研报告 */
    private Integer researchReports;

    /** 发表文章 */
    private Integer publishArticle;

    /** 重点工作 */
    private Integer keyTasks;

    /** 获奖情况 */
    private Integer awards;

    /** 公益情况 */
    private Integer charity;
}
