package com.ruoyi.project.committee.archive.controller;


import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.committee.archive.domain.dto.CommitteeMemberQueryDTO;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo;
import com.ruoyi.project.committee.archive.service.ICommitteeMemberService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/committee/member")
public class CommitteeMemberController extends BaseController {

    @Resource
    private ICommitteeMemberService committeeMemberService;

    @GetMapping
    public AjaxResult getCommitteeMember(@RequestParam String userId) {
        return AjaxResult.success(committeeMemberService.getMemberInfo(userId));
    }

    /**
     * 根据条件查询委员信息
     * @param queryDTO 查询条件DTO
     * @return 委员信息列表
     */
    @GetMapping("/query")
    public AjaxResult queryCommitteeMembers(CommitteeMemberQueryDTO queryDTO) {
        return success(committeeMemberService.selectCommitteeMemberByCondition(queryDTO));
    }
}
