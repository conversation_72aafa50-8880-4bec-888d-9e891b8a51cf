package com.ruoyi.project.committee.archive.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 界别信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_sector")
public class Sector extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 父级id
     */
    private Long fatherGroupId;

    /**
     * 界别名称
     */
    private String sectorName;

    /**
     * 界别类型
     */
    private String sectorType;

    /**
     * 界别名称缩写
     */
    private String sectorNameAbbr;

    /**
     * 联系电话
     */
    private String sectorTel;

    /**
     * 地址
     */
    private String sectorAddress;

    /**
     * 编码
     */
    private String sectorZipCode;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 联系人职责
     */
    private String contactsDuty;

    /**
     * 联系人电话
     */
    private String contactsPhone;

    /**
     * 联系人邮箱
     */
    private String contactsEmail;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 启用状态
     */
    private Boolean isEnable;


    /**
     * 逻辑删除标志位
     */
    @TableLogic
    private Boolean delFlag;
}
