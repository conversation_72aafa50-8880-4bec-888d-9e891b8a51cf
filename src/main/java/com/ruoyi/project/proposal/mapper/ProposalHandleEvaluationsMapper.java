package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalHandleEvaluations;
import org.apache.ibatis.annotations.Mapper;


/**
 * 提案办理情况评价Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
@Mapper
public interface ProposalHandleEvaluationsMapper extends BaseMapper<ProposalHandleEvaluations> {

    default ProposalHandleEvaluations selectHandleEvaluationsById(String handleId) {
        LambdaQueryWrapper<ProposalHandleEvaluations> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProposalHandleEvaluations::getHandleId, handleId);

        return selectOne(queryWrapper);
    }
}
