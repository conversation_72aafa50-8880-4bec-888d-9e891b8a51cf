package com.ruoyi.project.proposal.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalVerifyRecord;
import com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提案审核记录信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Mapper
public interface ProposalVerifyRecordMapper extends BaseMapper<ProposalVerifyRecord> {

    /**
     * 查询提案审核记录信息列表
     * 
     * @param proposalVerifyRecord 提案审核记录信息
     * @return 提案审核记录信息集合
     */
    public List<ProposalVerifyRecord> selectProposalVerifyRecordList(ProposalVerifyRecord proposalVerifyRecord);


    /**
     * 批量删除提案审核记录信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProposalVerifyRecordByIds(Long[] ids);


    public List<ProposalVerifyRecordVo> selectProposalVerifyRecordByProposalId(Long proposalId);
}
