package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalSupervisionRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ProposalSupervisionRecordMapper extends BaseMapper<ProposalSupervisionRecord> {

    default List<ProposalSupervisionRecord> getSupervisionRecords(String proposalId) {
        return selectList(new LambdaQueryWrapper<ProposalSupervisionRecord>()
                .eq(ProposalSupervisionRecord::getProposalId, proposalId)
                .orderByDesc(ProposalSupervisionRecord::getCreateTime));
    };
}
