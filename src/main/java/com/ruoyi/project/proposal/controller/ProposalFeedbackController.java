package com.ruoyi.project.proposal.controller;


import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.proposal.domain.dto.ProposalFeedbackDto;
import com.ruoyi.project.proposal.service.IProposalFeedbackService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/proposal/feedback")
public class ProposalFeedbackController extends BaseController {

    @Resource
    private IProposalFeedbackService proposalFeedbackService;

    /**
     * 获取提案反馈
     * @param proposalId 提案id
     * @return 提案反馈信息
     */
//    @PreAuthorize("@ss.hasPermi('system:feedback:list')")
    @GetMapping("/getFeedback")
    public AjaxResult getFeedbackList(@RequestParam String proposalId) {
        return AjaxResult.success(proposalFeedbackService.selectProposalFeedbackVoById(proposalId));
    }


    /**
     * 办理答复
     * @param feedbackDto feedbackDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:reply')")
    @PostMapping
    public AjaxResult add(@RequestBody ProposalFeedbackDto feedbackDto) {
        return AjaxResult.success(proposalFeedbackService.insertProposalFeedback(feedbackDto));
    }

    /**
     * 获取承办单位反馈信息
     * @param proposalId 提案id
     * @return 承办单位反馈信息
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:reply:query')")
    @GetMapping("/getUnitFeedback")
    public AjaxResult getUnitFeedback(@RequestParam String proposalId) {
        return AjaxResult.success(proposalFeedbackService.getUnitFeedback(proposalId));
    }

    @PreAuthorize("@ss.hasPermi('system:proposal:feedback:editAnnex')")
    @PostMapping("/editAnnex")
    public AjaxResult editAnnex(@RequestBody ProposalFeedbackDto feedbackDto) {
        return AjaxResult.success(proposalFeedbackService.updateProposalFeedbackAnnex(feedbackDto));
    }
}
