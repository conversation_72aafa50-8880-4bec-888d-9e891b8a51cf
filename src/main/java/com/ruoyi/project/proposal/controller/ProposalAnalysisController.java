package com.ruoyi.project.proposal.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.proposal.domain.dto.ProposalAnalysisDTO;
import com.ruoyi.project.proposal.domain.vo.ProposalAnalysisOverviewVO;
import com.ruoyi.project.proposal.domain.vo.ProposalStatusStatisticsVO;
import com.ruoyi.project.proposal.service.IProposalAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 提案分析控制器
 */
@Slf4j
@Api(tags = "提案分析管理")
@RestController
@RequestMapping("/proposal/analysis")
public class ProposalAnalysisController extends BaseController {

    @Resource
    private IProposalAnalysisService proposalAnalysisService;

    /**
     * 获取提案概览统计数据
     * 支持按相对时间维度进行统计
     *
     * @param timeDimension 时间维度参数，可选值：week、month、quarter、year
     * @return 提案概览统计数据
     */
    @ApiOperation(value = "获取提案概览统计数据",
            notes = "支持按相对时间维度进行统计：" +
                    "week-本周，month-当前月，quarter-前3个月，year-当前年。" +
                    "如果不传timeDimension参数，默认返回当前年的数据。")
    @GetMapping("/overview")
    public AjaxResult getProposalOverview(
            @RequestParam(value = "timeDimension", defaultValue = "year") String timeDimension) {

        ProposalAnalysisDTO dto = new ProposalAnalysisDTO();
        dto.setTimeDimension(timeDimension);
        ProposalAnalysisOverviewVO result = proposalAnalysisService.getProposalOverview(dto);
        return AjaxResult.success(result);
    }

    /**
     * 获取提案状态统计数据
     * 支持按相对时间维度进行统计，返回7个状态的详细数量
     *
     * @param timeDimension 时间维度参数，可选值：week、month、quarter、year
     * @return 提案状态统计数据
     */
    @ApiOperation(value = "获取提案状态统计数据",
            notes = "支持按相对时间维度进行统计：" +
                    "week-本周，month-当前月，quarter-前3个月，year-当前年。" +
                    "返回7个状态的详细统计数据，适用于饼图、折线图和柱状图。")
    @GetMapping("/status")
    public AjaxResult getProposalStatusStatistics(
            @RequestParam(value = "timeDimension", defaultValue = "year") String timeDimension) {
        ProposalAnalysisDTO dto = new ProposalAnalysisDTO();
        dto.setTimeDimension(timeDimension);

        ProposalStatusStatisticsVO result = proposalAnalysisService.getProposalStatusStatistics(dto);

        return AjaxResult.success(result);
    }


}
