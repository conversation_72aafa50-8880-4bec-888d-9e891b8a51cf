package com.ruoyi.project.proposal.controller;

import java.net.URLEncoder;
import java.text.ParseException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.proposal.domain.dto.FileCaseDto;
import com.ruoyi.project.proposal.domain.dto.MergeCaseDto;
import com.ruoyi.project.proposal.domain.vo.*;
import com.ruoyi.project.proposal.service.IProposalSupervisionRecordService;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.project.proposal.service.IProposalService;


/**
 * 提案信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/proposal")
public class ProposalController extends BaseController {

    @Autowired
    private IProposalService proposalService;

    @Autowired
    private IProposalSupervisionRecordService supervisionRecordService;

    /**
     * 立案审查分页查询
     * @param pageParam 分页查询参数
     * @return 提案分页查询结果
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:list')")
    @PostMapping("/page")
    public TableDataInfo getByPage(@RequestBody ProposalPageParamVo pageParam) {
        pageParam.setCaseFiling(CaseFillingEnum.WAIT_PUT_ON);
        IPage<ProposalPageVo> proposalPageVoIPage = proposalService.selectProposalPage(pageParam);
        return getDataTable(proposalPageVoIPage.getRecords(), proposalPageVoIPage.getTotal());
    }

    /**
     * 后期处理分页查询
     * @param pageParam 分页查询参数
     * @return 后期处理分页查询结果
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:list')")
    @PostMapping("/postProcessPage")
    public TableDataInfo getPostProcessPage(@RequestBody ProposalPageParamVo pageParam) {
        IPage<ProposalPageVo> proposalPageVoIPage = proposalService.selectPostProcessPage(pageParam);
        return getDataTable(proposalPageVoIPage.getRecords(), proposalPageVoIPage.getTotal());
    }


    /**
     * 提案交办
     * 获取提案交办分页信息
     * @param pageParam 分页查询参数
     * @return 查询结果
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:list')")
    @PostMapping("/assignPage")
    public TableDataInfo getAssignPageInfo(@RequestBody ProposalPageParamVo pageParam) {
        IPage<ProposalAssignPageVo> assignPageVoIPage = proposalService.selectProposalAssignPage(pageParam);
        return getDataTable(assignPageVoIPage.getRecords(), assignPageVoIPage.getTotal());
    }


    /**
     * 综合查询分页查询
     * @param pageParam 分页查询参数
     * @return 提案分页查询结果
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:list')")
    @PostMapping("/comprehensivePage")
    public TableDataInfo getByComprehensivePage(@Validated @RequestBody ProposalPageParamVo pageParam) {
        IPage<ProposalComprehensivePageVo> comprehensivePageVoIPage = proposalService.selectComprehensiveProposalPage(pageParam);
        return getDataTable(comprehensivePageVoIPage.getRecords(), comprehensivePageVoIPage.getTotal());
    }

    @PostMapping("/getUserProposal")
    public TableDataInfo getUserProposal(@RequestBody ProposalPageParamVo pageParam) {
        IPage<ProposalComprehensivePageVo> userProposalPage = proposalService.getUserProposal(pageParam);
        return getDataTable(userProposalPage.getRecords(), userProposalPage.getTotal());
    }

    /**
     * 立案审查
     * 根据提案id获取提案详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:query')")
    @GetMapping(value = "/getById")
    public AjaxResult getInfoById(@Param("id") String id) {
        return success(proposalService.selectProposalById(id));
    }


    /**
     * 综合查询
     * 根据提案id获取提案综合信息
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:query')")
    @GetMapping(value = "/getComprehensiveById")
    public AjaxResult getComprehensiveInfoById(@Param("id") String id) {
        return success(proposalService.selectComprehensiveInfoById(id));
    }

    /**
     * 新增提案信息
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:add')")
    @Log(title = "提案信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProposalEditVo proposalEditVo) {
        return toAjax(proposalService.insertProposal(proposalEditVo));
    }

    /**
     * 修改提案信息
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:edit')")
    @Log(title = "提案信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProposalEditVo proposalEditVo) {
        int result = proposalService.updateProposal(proposalEditVo);
        return result < 0 ? AjaxResult.warn("无权限修改") : AjaxResult.success();
    }

    /**
     * 删除提案信息
     */
//    @PreAuthorize("@ss.hasPermi('system:proposal:remove')")
//    @DeleteMapping("/delete")
//    public AjaxResult deleteProposal(@RequestParam("id") String id) {
//        return AjaxResult.success(proposalService.deleteProposal(id));
//    }

    /**
     * 更新提案交办状态
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:edit')")
    @Log(title = "提案信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updateAssign")
    public AjaxResult batchUpdateAssignStatus(@RequestParam("ids") String[] ids) {
        return AjaxResult.success(proposalService.updateProposalAssignStatus(ids));
    }


    /**
     * 批量删除提案信息
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:remove')")
    @Log(title = "提案信息", businessType = BusinessType.EXPORT)
    @PostMapping("/batchDelete")
    public AjaxResult batchDeleteProposal(@RequestBody ProposalEditVo editVo) {
        int result = proposalService.batchDeleteProposal(editVo.getIds());
        return result == -1 ? AjaxResult.warn("无权限操作，不可删除其他人的提案") : AjaxResult.success();
    }

    /**
     * 导出提案信息Word
     */
//    @PreAuthorize("@ss.hasPermi('system:proposal:export')")
    @Log(title = "提案信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, String id) throws ParseException {

        ProposalWordVo proposalWordVo = proposalService.selectProposalWordVoById(id);
        try {
            ClassPathResource templateFile = new ClassPathResource("template/original_proposal_template.docx");
            XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(),
                    BeanUtil.beanToMap(proposalWordVo));

            String fileName = "提案信息.docx";
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            ServletOutputStream out = response.getOutputStream();
            document.write(out);
            out.flush();
            out.close();

        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }


    /**
     * 提案督察分页
     * @param pageParam 分页查询参数
     * @return 督察数据
     */
    @PostMapping("/getSupervisionPage")
    public TableDataInfo getSupervisionPage(@RequestBody ProposalPageParamVo pageParam) {

        IPage<SupervisionPageVo> supervisionPage = proposalService.getSupervisionPage(pageParam);
        return getDataTable(supervisionPage.getRecords(), supervisionPage.getTotal());
    }

    @GetMapping("/urge")
    public AjaxResult urge(@RequestParam("id") String id) {
        return AjaxResult.success(proposalService.urge(id));
    }

    @GetMapping("/getSupervisionRecord")
    public TableDataInfo getSupervisionRecord(@RequestParam("id") String id) {
        return getDataTable(supervisionRecordService.getSupervisionRecords(id));
    }

    /**
     * 立案
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:file')")
    @PostMapping("/fileCase")
    public AjaxResult fileCase(@RequestBody FileCaseDto fileCaseDto) {
        return AjaxResult.success(proposalService.fileCase(fileCaseDto));
    }

    /**
     * 不立案
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:notFile')")
    @PostMapping("/notFileCase")
    public AjaxResult notFileCase(@RequestBody FileCaseDto fileCaseDto) {
        return AjaxResult.success(proposalService.notFileCase(fileCaseDto));
    }

    /**
     * 并案
     * @param mergeCaseDto mergeCaseDto
     * @return Boolean
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:merge')")
    @PostMapping("/mergeCase")
    public AjaxResult mergeCase(@RequestBody MergeCaseDto mergeCaseDto) {
        return AjaxResult.success(proposalService.mergeCase(mergeCaseDto));
    }

    /**
     * 撤案
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:cancel')")
    @PostMapping("/cancelCase")
    public AjaxResult cancelCase(@RequestBody FileCaseDto fileCaseDto) {
        return AjaxResult.success(proposalService.cancelCase(fileCaseDto));
    }

    /**
     * 提案交办
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:assign')")
    @PostMapping("/assign")
    public AjaxResult assignCase(@RequestBody FileCaseDto fileCaseDto) {
        return AjaxResult.success(proposalService.assignCase(fileCaseDto));
    }

    /**
     * 提案-改办
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    @PreAuthorize("@ss.hasPermi('system:proposal:changeHandle')")
    @PostMapping("/changeHandle")
    public AjaxResult changeHandle(@RequestBody FileCaseDto fileCaseDto) {
        return AjaxResult.success(proposalService.changeHandle(fileCaseDto));
    }

    /**
     * 获取立案信息
     * @param proposalId proposalId
     * @return FileCaseVo
     */
    @GetMapping("/getFileCaseInfo")
    public AjaxResult getFileCaseInfo(@RequestParam("proposalId") String proposalId) {
        return AjaxResult.success(proposalService.getFileCaseInfo(proposalId));
    }
}

