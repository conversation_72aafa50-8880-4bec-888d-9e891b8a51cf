package com.ruoyi.project.proposal.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.project.proposal.domain.ProposalQualityEvaluation;
import com.ruoyi.project.proposal.service.IProposalQualityEvaluationService;
import com.ruoyi.common.utils.poi.ExcelUtil;


/**
 * 质量评价信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/proposal/quality")
public class ProposalQualityEvaluationController extends BaseController {

    @Autowired
    private IProposalQualityEvaluationService proposalQualityEvaluationService;


    /**
     * 查询质量评价信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/getEvaList")
    public AjaxResult getQualityEvaluationList(@RequestParam(value = "proposalId") Long proposalId) {
        return AjaxResult.success(proposalQualityEvaluationService.selectEvaluationList(proposalId));
    }


    /**
     * 获取质量评价信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:evaluation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(proposalQualityEvaluationService.selectProposalQualityEvaluationById(id));
    }

    /**
     * 新增质量评价信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:add')")
    @Log(title = "质量评价信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProposalQualityEvaluation proposalQualityEvaluation) {
        return toAjax(proposalQualityEvaluationService.insertProposalQualityEvaluation(proposalQualityEvaluation));
    }

    /**
     * 修改质量评价信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:edit')")
    @Log(title = "质量评价信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProposalQualityEvaluation proposalQualityEvaluation) {
        return toAjax(proposalQualityEvaluationService.updateProposalQualityEvaluation(proposalQualityEvaluation));
    }

    /**
     * 删除质量评价信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:remove')")
    @Log(title = "质量评价信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(proposalQualityEvaluationService.deleteProposalQualityEvaluationByIds(ids));
    }
}
