package com.ruoyi.project.proposal.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordEditVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.project.proposal.domain.ProposalVerifyRecord;
import com.ruoyi.project.proposal.service.IProposalVerifyRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;


/**
 * 提案审核记录信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/proposal/verify/record")
public class ProposalVerifyRecordController extends BaseController {

    @Autowired
    private IProposalVerifyRecordService proposalVerifyRecordService;


    /**
     * 导出提案审核记录信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "提案审核记录信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProposalVerifyRecord proposalVerifyRecord) {
        List<ProposalVerifyRecord> list = proposalVerifyRecordService.selectProposalVerifyRecordList(proposalVerifyRecord);
        ExcelUtil<ProposalVerifyRecord> util = new ExcelUtil<ProposalVerifyRecord>(ProposalVerifyRecord.class);
        util.exportExcel(response, list, "提案审核记录信息数据");
    }

    /**
     * 获取提案审核记录信息列表
     * @param proposalId 提案id
     * @return recordList
     */
    @GetMapping("/getByProposalId")
    public AjaxResult getRecordList(@RequestParam Long proposalId) {
        return AjaxResult.success(proposalVerifyRecordService.selectProposalVerifyRecordList(proposalId));
    }
}
