package com.ruoyi.project.proposal.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.InstructionEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalComprehensivePageVo {

    private String id;

    /** 提案年度 */
    private Long year;

    /** 流水号 */
    private Integer serialNumber;

    /** 提案案号 */
    private String caseNumber;

    /** 提案案别 */
    private String caseType;

    /** 提案案由 */
    private String caseReason;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /** 答复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackDate;

    /** 承办单位 */
    private String organizers;

    /** 办理方式 */
    private String undertakeWay;

    private CaseFillingEnum caseFiling;

    /** 提案人 */
    private String proposer;

    @JsonIgnore
    private String handleId;

    private List<String> organizerList;
}
