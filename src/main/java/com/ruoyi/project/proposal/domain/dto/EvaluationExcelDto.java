package com.ruoyi.project.proposal.domain.dto;


import com.ruoyi.common.annotation.Header;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EvaluationExcelDto {

    private String proposalId;

    private String handleId;

    private Integer no;

    private Integer year;

    private String proposer;

    private String organizer;

    private String undertakeResult;

    private String undertakePerson;

    private String handleWay;

    private String proposalQuality;

    private String isTimelyResponse;

    private String hasFormalReport;

    private String hasDedicatedHandler;

    private String isLeadersSignature;

    private String hasPrePostNegotiation;

    private String isStandardHandover;

    private String adoptOrPartiallyAdopt;

    private String needToImplement;

    private String solvedOrPartiallySolved;

    private String noSolution;

    private String explainedClearly;

    private String notExplained;

    private Integer businessAbility;

    private Integer activeCommunication;

    private Integer sufficientExchange;

    private Integer targetedProcessing;

    private Integer consensusOnProblems;

    private BigDecimal overallEvaluation;

    private String otherOpinions;
}
