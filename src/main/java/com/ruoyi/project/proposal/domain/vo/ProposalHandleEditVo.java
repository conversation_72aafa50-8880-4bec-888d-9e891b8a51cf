package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.UndertakeResultEnum;
import com.ruoyi.common.enums.proposal.HandleWayEnum;
import com.ruoyi.common.enums.proposal.QualityEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class ProposalHandleEditVo {

    @ApiModelProperty(value = "提案id")
    private String proposalId;

    @ApiModelProperty(value = "承办单位")
    private String organizers;

    @ApiModelProperty(value = "承办结果")
    private UndertakeResultEnum undertakeResult;

    @ApiModelProperty(value = "承办人员")
    private String undertakePerson;

    @ApiModelProperty(value = "承办方式")
    private String undertakeWay;

    @ApiModelProperty(value = "提案质量")
    private QualityEnum proposalQuality;

    @ApiModelProperty(value = "办理方式")
    private HandleWayEnum handleWay;

    /** 办结日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "办结日期")
    private Date undertakeTime;

    @ApiModelProperty(value = "承办单位列表")
    private List<Long> organizerList;
}
