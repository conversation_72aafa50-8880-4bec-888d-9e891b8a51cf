package com.ruoyi.project.proposal.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 提案人员关联对象 proposal_user_rel
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalUserRel  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 提案id */
    private String proposalId;

    /** 提案者id */
    private String proposerId;

    /** 提交类型 */
    private String submitType;

    /** 逻辑删除标志位 */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}
