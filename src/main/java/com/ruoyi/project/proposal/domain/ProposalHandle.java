package com.ruoyi.project.proposal.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 办理信息对象 proposal_handle
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalHandle extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 提案id */
    private String proposalId;

    /** 承办单位 */
    @Excel(name = "承办单位")
    private String organizers;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String undertakeResult;

    /** 承办人员 */
    @Excel(name = "承办人员")
    private String undertakePerson;

    /** 办理方式 */
    @Excel(name = "办理方式")
    private String undertakeWay;

    /** 提案质量 */
    @Excel(name = "提案质量")
    private String proposalQuality;

    /** 办理方式 */
    private String handleWay;

    /** 办结日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "办结日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date undertakeTime;

    @Excel(name = "评价状态")
    private Boolean evaluationStatus;


    @Excel(name = "反馈状态")
    private Boolean feedbackStatus;


    @Excel(name = "退回状态")
    private Boolean revertStatus;

    /** 是否发送短信 */
    private Boolean willSendSms;

    /** 删除标识位 */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;


}
