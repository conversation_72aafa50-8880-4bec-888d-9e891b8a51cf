package com.ruoyi.project.proposal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProposalReplyFeedbackVo {

    @ApiModelProperty(value = "主办反馈意见")
    private ProposalFeedbackVo leadFeedback;

    @ApiModelProperty(value = "协办反馈意见列表")
    private List<ProposalFeedbackVo>  coFeedbackList = new ArrayList<>();

    public void addCoFeedback(ProposalFeedbackVo feedbackVo) {
        this.coFeedbackList.add(feedbackVo);
    }

}
