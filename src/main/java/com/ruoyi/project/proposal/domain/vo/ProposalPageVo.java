package com.ruoyi.project.proposal.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.InstructionEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalPageVo {

    private String id;

    /** 提案年度 */
    private Long year;

    /** 流水号 */
    private Integer serialNumber;

    /** 提案案号 */
    private String caseNumber;

    /** 提案案由 */
    private String caseReason;

    /** 提案类别 */
    private String caseType;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerDate;

    /** 立案情况(立案，已撤案，不立案，已并案，待立案，带办理， 办结) */
    private String caseFiling;

    /** 提案人 */
    private String proposer;

    /** 提案人列表 */
//    private List<ProposalUserVo> userList;
}
