package com.ruoyi.project.proposal.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.HandleWayEnum;
import com.ruoyi.common.enums.proposal.QualityEnum;
import com.ruoyi.common.enums.proposal.UndertakeResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalFeedbackDto {

    @ApiModelProperty(value = "反馈信息id")
    private String id;

    @ApiModelProperty(value = "提案id")
    private String proposalId;

    @ApiModelProperty(value = "办理结果")
    private UndertakeResultEnum undertakeResult;

    @ApiModelProperty(value = "办理方式")
    private HandleWayEnum handleWay;

    @ApiModelProperty(value = "公开类型")
    private Boolean isOpen;

    @ApiModelProperty(value = "提案质量")
    private QualityEnum proposalQuality;

    @ApiModelProperty(value = "签发领导")
    private String issueUser;

    @ApiModelProperty(value = "承办人员")
    private String undertakePerson;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "办结日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date undertakeTime;

    @ApiModelProperty(value = "答复内容")
    private String content;

    @ApiModelProperty(value = "附件id列表")
    private List<String> annexIdList;
}
