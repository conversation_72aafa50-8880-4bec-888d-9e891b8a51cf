package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class SupervisionPageVo {

    private String id;

    private Integer year;

    private String caseNumber;

    private String caseReason;

    private String proposer;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    private String organizer;

    private String overdue;

    private Integer urgeCount;
}
