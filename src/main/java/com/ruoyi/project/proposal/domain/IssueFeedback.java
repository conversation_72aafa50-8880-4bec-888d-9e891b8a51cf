package com.ruoyi.project.proposal.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IssueFeedback extends BaseEntity {

    @TableId
    private String id;

    private String content;

    private String feedback;

    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}
