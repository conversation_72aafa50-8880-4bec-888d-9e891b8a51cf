package com.ruoyi.project.proposal.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.enums.proposal.HandleWayEnum;
import com.ruoyi.common.enums.proposal.QualityEnum;
import com.ruoyi.common.enums.proposal.UndertakeResultEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalFeedback extends BaseEntity {

    private String id;

    /** 提案id */
    private String proposalId;

    /** 答复人id */
    private String replyUserId;

    /** 承办方式 */
    private UndertakeWayEnum undertakeWay;

    /** 答复内容 */
    private String content;

    /** 办理结果 */
    private UndertakeResultEnum undertakeResult;

    /** 办理方式 */
    private HandleWayEnum handleWay;

    /** 办理人员 */
    private String undertakePerson;

    /** 联系电话 */
    private String contactPhone;

    /** 提案质量 */
    private QualityEnum proposalQuality;

    /** 签发领导 */
    private String issueUser;

    /** 公开类型 */
    private Boolean isOpen;

    /** 办结日期 */
    private Date finishTime;

    @TableField(exist = false)
    private String remark;
}