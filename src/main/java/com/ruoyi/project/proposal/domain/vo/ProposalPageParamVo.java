package com.ruoyi.project.proposal.domain.vo;

import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.CaseTypeEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
public class ProposalPageParamVo {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    private String userId;

    @ApiModelProperty(value = "提案年度")
    private Integer[] year;

    @ApiModelProperty(value = "提案类别")
    private CaseTypeEnum caseType;

    @ApiModelProperty(value = "提案案由")
    private String caseReason;

    @ApiModelProperty(value = "流水号")
    @Pattern(regexp = "\\d+", message = "流水号必须为数字")
    private String serialNumber;

    @ApiModelProperty(value = "提案案号")
    @Pattern(regexp = "\\d+", message = "案号必须为数字")
    private String caseNumber;

    @ApiModelProperty(value = "是否终审")
    private Boolean isFinalTrial;

    @ApiModelProperty(value = "是否交办")
    private Boolean isAssigned;

    @ApiModelProperty(value = "是否接收")
    private Boolean isReceived;

    @ApiModelProperty(value = "提交类型")
    private SubmitTypeEnum submitType;

    @ApiModelProperty(value = "立案情况")
    private CaseFillingEnum caseFiling;

    @ApiModelProperty(value = "提案者")
    private String proposer;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted = false;

    @ApiModelProperty(value = "案号状态（true：案号为空, false: 案号不为空）")
    private Boolean caseNumberIsNull;

    @ApiModelProperty(value = "承办方式")
    private String undertakeWay;

    @ApiModelProperty(value = "是否办结")
    private Boolean isFinished;

    @ApiModelProperty(value = "是否超期")
    private Boolean overdue;

    @ApiModelProperty(value = "用户名称")
    private String username;
}
