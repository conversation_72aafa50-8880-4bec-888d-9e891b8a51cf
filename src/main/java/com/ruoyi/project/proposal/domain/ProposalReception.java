package com.ruoyi.project.proposal.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 提案接受情况信息对象 proposal_reception
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalReception extends BaseEntity {

    /** id */
    private String id;

    /** 提案id */
    @Excel(name = "提案id")
    private String proposalId;

    /** 接收人id */
    @Excel(name = "接收人id")
    private Long recipientId;

    /** 交办日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "交办日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date joinTime;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveTime;

    /** 接收状态 */
    @Excel(name = "接收状态")
    private Boolean receiveStatus;

    /** 承办方式 */
    @Excel(name = "承办方式")
    private String undertakeWay;

    /** 删除标识位 */
    @TableLogic
    private Boolean delFlag;

}
