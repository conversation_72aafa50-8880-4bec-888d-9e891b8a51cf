package com.ruoyi.project.proposal.domain.vo;

import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FileCaseVo {

    @ApiModelProperty(value = "提案id")
    private String proposalId;

    @ApiModelProperty(value = "承办方式")
    private UndertakeWayEnum undertakeWay;

    @ApiModelProperty(value = "是否公开")
    private Boolean isOpen;

    @ApiModelProperty(value = "是否发送短信")
    private Boolean willSendSms;

    @ApiModelProperty(value = "承办单位")
    private List<UndertakeUnitVo> undertakeUnits;

}
