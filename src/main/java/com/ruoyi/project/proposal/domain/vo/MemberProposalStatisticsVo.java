package com.ruoyi.project.proposal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 委员提案统计VO
 */
@Data
public class MemberProposalStatisticsVo {

    @ApiModelProperty(value = "委员姓名")
    private String memberName;

    @ApiModelProperty(value = "单独或领衔")
    private Long individualCount = 0L;

    @ApiModelProperty(value = "附议")
    private Long motionCount = 0L;

    @ApiModelProperty(value = "重要提案")
    private Long importantCount = 0L;

    @ApiModelProperty(value = "优秀提案")
    private Long excellentCount = 0L;

    /**
     * 计算总数
     */
    public Long getTotalCount() {
        return individualCount + motionCount + importantCount + excellentCount;
    }
}
