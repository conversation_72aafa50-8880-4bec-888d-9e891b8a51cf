package com.ruoyi.project.proposal.domain;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 提案办理信息关联对象 proposal_handle_rel
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
public class ProposalHandleRel
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 提案id */
    @Excel(name = "提案id")
    private String proposalId;

    /** 办理信息id */
    @Excel(name = "办理信息id")
    private String handleId;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("proposalId", getProposalId())
            .append("handleId", getHandleId())
            .toString();
    }
}
