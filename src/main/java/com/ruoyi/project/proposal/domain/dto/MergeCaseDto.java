package com.ruoyi.project.proposal.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MergeCaseDto {

    @ApiModelProperty(value = "主提案id")
    private String mainCaseId;

    @ApiModelProperty(value = "案号")
    private Integer caseNumber;

    @ApiModelProperty(value = "合并的提案列表")
    private List<MergeCaseDetailDto> mergeCaseList;
}
