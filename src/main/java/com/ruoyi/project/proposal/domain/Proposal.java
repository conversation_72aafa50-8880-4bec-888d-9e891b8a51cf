package com.ruoyi.project.proposal.domain;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.CaseTypeEnum;
import com.ruoyi.common.enums.proposal.InstructionEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import com.ruoyi.project.proposal.utils.ListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 提案信息对象 proposal
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Proposal extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 提案年度 */
    @Excel(name = "提案年度")
    private Long year;

    /** 流水号 */
    @Excel(name = "流水号")
    private Integer serialNumber;

    /** 提案案号 */
    @Excel(name = "提案案号")
    private Integer caseNumber;

    /** 提案类别(经济,政治,文化,社会,生态文明,其他) */
    @Excel(name = "提案类别(经济,政治,文化,社会,生态文明,其他)")
    private String caseType;

    @Excel(name = "提案案别")
    private String caseCategory;

    /** 提案案由 */
    @Excel(name = "提案案由")
    private String caseReason;

    /** 提案内容 */
    @Excel(name = "提案内容")
    private String caseContent;

    @Excel(name = "提案者")
    private String proposer;

    /** 相关情况 */
    @Excel(name = "相关情况")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<InstructionEnum> instructions;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registerDate;

    /** 提交类型(个人，个人联名，集体) */
    @Excel(name = "提交类型(个人，个人联名，集体)")
    private SubmitTypeEnum submitType;

    /** 提案状态(立案，已撤案，不立案，已并案，待立案，带办理， 办结) */
    @Excel(name = "立案情况(立案，已撤案，不立案，已并案，待立案，带办理， 办结)")
    private CaseFillingEnum caseFiling;

    /** 改办状态 */
    @Excel(name = "改办状态")
    private String retransactStatus;

    @Excel(name = "跟踪情况")
    private String track;

    /** 是否公开 */
    @Excel(name = "是否公开")
    private Boolean isOpen;

    /** 是否终审 */
    @Excel(name = "是否终审")
    private Boolean isFinalTrial;

    /** 是否交办 */
    @Excel(name = "是否交办")
    private Boolean isAssigned;

    @Excel(name = "是否接收")
    private Boolean isReceived;

    @Excel(name = "是否办结")
    private Boolean isFinished;

    /** 删除标识位 */
    @TableLogic
    private Integer delFlag;

}
