package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.CaseTypeEnum;
import com.ruoyi.common.enums.proposal.InstructionEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalVo {

    /** id */
    private String id;

    /** 提案年度 */
    private Long year;

    /** 流水号 */
    private Integer serialNumber;

    /** 提案案号 */
    private String caseNumber;

    /** 提案人 */
    private String proposer;

    /** 提案类别(经济,政治,文化,社会,生态文明,其他) */
    private CaseTypeEnum caseType;

    /** 提案案由 */
    private String caseReason;

    /** 提案内容 */
    private String caseContent;

    /** 相关情况 */
    private List<InstructionEnum> instructions;

    /** 立案情况(立案，已撤案，不立案，已并案，待立案，带办理， 办结) */
    private String caseFiling;

    /** 是否公开 */
    private Boolean isOpen;

    /** 备注内容 */
    private String remark;

    /** 提交类型 */
    private SubmitTypeEnum submitType;

    /** 提案人列表 - 个人/个人联名 */
    private List<ProposalUserRelVo> userList;

    /** 提案者列表 - 集体 */
    private List<Object> proposerList;
}
