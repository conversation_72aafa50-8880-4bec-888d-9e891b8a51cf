package com.ruoyi.project.proposal.domain.dto;

import com.ruoyi.common.enums.proposal.MeasureEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import com.ruoyi.common.enums.proposal.VerifyReasonEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FileCaseDto {

    @ApiModelProperty(value = "提案id")
    private String proposalId;

    @ApiModelProperty(value = "案号")
    private String caseNumber;

    @ApiModelProperty(value = "承办方式")
    private UndertakeWayEnum undertakeWay;

    @ApiModelProperty(value = "是否公开")
    private Boolean isOpen;

    @ApiModelProperty(value = "是否发送短信")
    private Boolean willSendSms;

    @ApiModelProperty(value = "审核人")
    private String auditor;

    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(value = "不立案理由")
    private List<VerifyReasonEnum> verifyReason;

    @ApiModelProperty(value = "处理措施")
    private List<MeasureEnum> measure;

    @ApiModelProperty(value = "相关单位")
    private String relateUnit;

    @ApiModelProperty(value = "承办单位")
    private List<UndertakeUnitDto> undertakeUnits;

}
