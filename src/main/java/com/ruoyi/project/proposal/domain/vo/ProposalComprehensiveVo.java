package com.ruoyi.project.proposal.domain.vo;

import com.ruoyi.common.enums.proposal.CaseTypeEnum;
import com.ruoyi.common.enums.proposal.InstructionEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProposalComprehensiveVo {

    private String id;

    private Integer year;

    @ApiModelProperty(value = "提案案号")
    private String caseNumber;

    @ApiModelProperty(value = "提案案由")
    private String caseReason;

    @ApiModelProperty(value = "提案内容")
    private String caseContent;

    @ApiModelProperty(value = "提交方式")
    private SubmitTypeEnum submitType;

    @ApiModelProperty(value = "提案案别")
    private String caseCategory;

    @ApiModelProperty(value = "提案类别")
    private CaseTypeEnum caseType;

    @ApiModelProperty(value = "相关情况")
    private List<InstructionEnum> instructions;

    @ApiModelProperty(value = "提案者")
    private String proposer;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否公开")
    private Boolean isOpen;

    @ApiModelProperty(value = "是否交办")
    private Boolean isAssigned;

    @ApiModelProperty(value = "承办单位")
    private String organizers;

    @ApiModelProperty(value = "承办方式")
    private String undertakeWay;

    @ApiModelProperty(value = "承办结果")
    private String undertakeResult;

    @ApiModelProperty(value = "承办日期")
    private String undertakeTime;

    @ApiModelProperty(value = "跟踪情况")
    private String track;

    @ApiModelProperty(value = "提案情状态")
    private String caseFiling;

    /** 提案者 - 提交类型为个人 */
    private List<ProposalUserRelVo> userList;

    /** 提案者 - 提交类型为集体 */
    private List<Object> proposerList;

    private List<String> organizerList;
}
