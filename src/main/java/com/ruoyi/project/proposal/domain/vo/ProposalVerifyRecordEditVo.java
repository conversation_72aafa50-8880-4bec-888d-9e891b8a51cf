package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 提案审核记录信息对象
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
public class ProposalVerifyRecordEditVo {

    private Long proposalId;

    private Long verifierId;

    private String verifyProcess;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    private String verifyLog;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("proposalId", getProposalId())
            .append("verifyProcess", getVerifyProcess())
            .append("verifyTime", getVerifyTime())
            .append("verifyLog", getVerifyLog())
            .toString();
    }
}
