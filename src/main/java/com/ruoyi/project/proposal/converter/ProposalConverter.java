package com.ruoyi.project.proposal.converter;

import com.ruoyi.project.proposal.domain.Proposal;
import com.ruoyi.project.proposal.domain.dto.ProposalToManuscriptDto;
import com.ruoyi.project.proposal.domain.vo.ProposalEditVo;
import com.ruoyi.project.proposal.domain.vo.ProposalVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProposalConverter {

    ProposalConverter INSTANCE = Mappers.getMapper(ProposalConverter.class);

    Proposal convert(ProposalEditVo vo);

    @Mapping(target = "caseNumber", expression = "java(proposal.getCaseNumber() == null ? null : String.format(\"%04d\", proposal.getCaseNumber()))")
    ProposalVo convert(Proposal proposal);

    ProposalToManuscriptDto convertToManuscript(Proposal proposal);
}
