package com.ruoyi.project.proposal.converter;

import com.ruoyi.project.proposal.domain.ProposalReception;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionEditVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ReceptionConverter {

    ReceptionConverter INSTANCE = Mappers.getMapper(ReceptionConverter.class);

    ProposalReception convert(ProposalReceptionEditVo receptionEditVo);
}
