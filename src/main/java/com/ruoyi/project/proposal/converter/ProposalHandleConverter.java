package com.ruoyi.project.proposal.converter;

import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.domain.dto.EvaluationExcelDto;
import com.ruoyi.project.proposal.domain.vo.EvaluationExcelVo;
import com.ruoyi.project.proposal.domain.vo.ProposalHandleEditVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProposalHandleConverter {

    ProposalHandleConverter INSTANCE = Mappers.getMapper(ProposalHandleConverter.class);

    @Mapping(target = "undertakeResult", expression = "java(vo.getUndertakeResult().getDescription())")
    @Mapping(target = "handleWay", expression = "java(vo.getHandleWay().getDescription())")
    @Mapping(target = "proposalQuality", expression = "java(vo.getProposalQuality().getDescription())")
    ProposalHandle convert(ProposalHandleEditVo vo);


    EvaluationExcelVo convert(EvaluationExcelDto dto);
}
