package com.ruoyi.project.proposal.converter;

import com.ruoyi.project.proposal.domain.IssueFeedback;
import com.ruoyi.project.proposal.domain.vo.IssueFeedbackVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface IssueFeedbackConverter {

    IssueFeedbackConverter INSTANCE = Mappers.getMapper(IssueFeedbackConverter.class);

    List<IssueFeedbackVo> convert(List<IssueFeedback> issueFeedbackList);
}
