package com.ruoyi.project.proposal.utils;


import com.ruoyi.common.enums.proposal.InstructionEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@MappedTypes(List.class)
public class ListTypeHandler extends BaseTypeHandler<List<InstructionEnum>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<InstructionEnum> parameter, JdbcType jdbcType) throws SQLException {
        String value = parameter.stream().map(InstructionEnum::name).collect(Collectors.joining(","));
        ps.setString(i, value);
    }

    @Override
    public List<InstructionEnum> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return value == null ? null : Arrays.stream(value.split(",")).map(InstructionEnum::valueOf).collect(Collectors.toList());
    }

    @Override
    public List<InstructionEnum> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return value == null ? null : Arrays.stream(value.split(",")).map(InstructionEnum::valueOf).collect(Collectors.toList());
    }

    @Override
    public List<InstructionEnum> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return value == null ? null : Arrays.stream(value.split(",")).map(InstructionEnum::valueOf).collect(Collectors.toList());
    }
}
