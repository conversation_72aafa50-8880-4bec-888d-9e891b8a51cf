package com.ruoyi.project.proposal.service;


import com.ruoyi.project.proposal.domain.ProposalHandleEvaluations;

import java.util.Map;

/**
 * 提案办理情况评价Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IProposalHandleEvaluationsService {

    /**
     * 根据id查询办理情况评价
     *
     * @param handleId id
     * @return 办理情况评价
     */
    Map<String, Map<String, Object>> getHandleEvaluationsById(String handleId);

    /**
     * 添加办理情况评价
     * @param handleEvaluations 办理情况信息
     * @return 添加结果
     */
    int addHandleEvaluation(ProposalHandleEvaluations handleEvaluations);
}
