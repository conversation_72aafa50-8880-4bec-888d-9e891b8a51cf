package com.ruoyi.project.proposal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.project.proposal.domain.Annex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.mapper.AnnexMapper;
import com.ruoyi.project.proposal.service.IAnnexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.List;


@Service
public class AnnexServiceImpl extends ServiceImpl<AnnexMapper, Annex> implements IAnnexService {

    @Resource
    private AnnexMapper annexMapper;


    @Override
    public List<AnnexVo> selectAnnexByProposalId(Long proposalId) {
        return annexMapper.selectAnnexByProposalId(proposalId);
    }

    @Override
    public void insertAnnex(Annex annex) {
        annexMapper.insert(annex);
    }
}
