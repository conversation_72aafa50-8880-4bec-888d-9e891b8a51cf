package com.ruoyi.project.proposal.service;

import java.util.List;
import com.ruoyi.project.proposal.domain.ProposalQualityEvaluation;
import com.ruoyi.project.proposal.domain.vo.ProposalQualityEvaluationVo;

/**
 * 质量评价信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IProposalQualityEvaluationService {
    /**
     * 查询质量评价信息
     * 
     * @param id 质量评价信息主键
     * @return 质量评价信息
     */
    public ProposalQualityEvaluation selectProposalQualityEvaluationById(Long id);

    /**
     * 查询质量评价信息列表
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 质量评价信息集合
     */
    public List<ProposalQualityEvaluation> selectProposalQualityEvaluationList(ProposalQualityEvaluation proposalQualityEvaluation);

    /**
     * 新增质量评价信息
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 结果
     */
    public int insertProposalQualityEvaluation(ProposalQualityEvaluation proposalQualityEvaluation);

    /**
     * 修改质量评价信息
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 结果
     */
    public int updateProposalQualityEvaluation(ProposalQualityEvaluation proposalQualityEvaluation);

    /**
     * 批量删除质量评价信息
     * 
     * @param ids 需要删除的质量评价信息主键集合
     * @return 结果
     */
    public int deleteProposalQualityEvaluationByIds(Long[] ids);

    /**
     * 删除质量评价信息信息
     * 
     * @param id 质量评价信息主键
     * @return 结果
     */
    public int deleteProposalQualityEvaluationById(Long id);

    /**
     * 根据提案id查询质量评价信息
     *
     * @param proposalId 提案id
     * @return 质量评价信息
     */
    List<ProposalQualityEvaluationVo> selectEvaluationList(Long proposalId);
}
