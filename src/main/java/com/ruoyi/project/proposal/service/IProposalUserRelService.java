package com.ruoyi.project.proposal.service;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.proposal.domain.ProposalUserRel;
import com.ruoyi.project.proposal.domain.vo.ProposalUserRelVo;
import com.ruoyi.project.proposal.domain.vo.ProposerVo;

/**
 * 提案人员关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IProposalUserRelService extends IService<ProposalUserRel> {

    List<ProposalUserRelVo> selectProposalUserListById(String proposalId);

    Set<String> selectByProposerId(String proposerId);

    void updateProposalUserRel(String proposalId, List<ProposerVo> proposerList);
}
