package com.ruoyi.project.proposal.service;

import com.ruoyi.project.proposal.domain.ProposalUndertakeUnit;
import com.ruoyi.project.proposal.domain.dto.UndertakeUnitDto;
import com.ruoyi.project.proposal.domain.vo.UndertakeUnitVo;

import java.util.List;

public interface IProposalUndertakeUnitService {

    void insertUndertakeUnit(Long handleId, List<UndertakeUnitDto> undertakeUnitDtoList);

    /**
     * 更新承办单位信息
     * @param handleId handleId
     * @param undertakeUnitDtoList undertakeUnitDtoList
     */
    void updateUndertakeUnit(Long handleId, List<UndertakeUnitDto> undertakeUnitDtoList);

    /**
     * 编辑承办单位信息
     * @param undertakeUnitDtoList undertakeUnitDtoList
     */
    void editUndertakeUnit(List<UndertakeUnitDto> undertakeUnitDtoList);

    List<UndertakeUnitVo> getUndertakeUnitList(Long aLong);

    List<ProposalUndertakeUnit> selectUndertakeUnitList(Long handleId);

    void deleteUndertakeUnit(Long handleId, List<UndertakeUnitDto> unitList);
}
