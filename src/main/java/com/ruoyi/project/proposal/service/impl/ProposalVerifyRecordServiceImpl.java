package com.ruoyi.project.proposal.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.enums.proposal.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.proposal.domain.dto.FileCaseDto;
import com.ruoyi.project.proposal.domain.dto.MergeCaseDto;
import com.ruoyi.project.proposal.domain.dto.UndertakeUnitDto;
import com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.proposal.mapper.ProposalVerifyRecordMapper;
import com.ruoyi.project.proposal.domain.ProposalVerifyRecord;
import com.ruoyi.project.proposal.service.IProposalVerifyRecordService;

/**
 * 提案审核记录信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class ProposalVerifyRecordServiceImpl implements IProposalVerifyRecordService {

    private static final String VERIFY_LOG_TEMPLATE = "审核建议:{0} 审核操作:{1} {2} 处理办法:{3} 审核结果：{4}";

    @Autowired
    private ProposalVerifyRecordMapper proposalVerifyRecordMapper;

    @Override
    public void addFileCaseVerifyRecord(FileCaseDto fileCaseDto) {
        ProposalVerifyRecord verifyRecord = new ProposalVerifyRecord();
        verifyRecord.setProposalId(fileCaseDto.getProposalId());
        verifyRecord.setVerifyType(CaseFillingEnum.PUT_ON.name());
        verifyRecord.setVerifier(fileCaseDto.getAuditor());
        verifyRecord.setVerifyProcess(VerifyProcessEnum.FINAL_TRAIL.getDescription());
        verifyRecord.setVerifyTime(DateUtils.getNowDate());
        verifyRecord.setVerifyLog(CaseFillingEnum.PUT_ON.getDescription());

        proposalVerifyRecordMapper.insert(verifyRecord);
    }

    @Override
    public void addNotFileCaseVerifyRecord(FileCaseDto fileCaseDto) {
        ProposalVerifyRecord verifyRecord = new ProposalVerifyRecord();
        verifyRecord.setProposalId(fileCaseDto.getProposalId());
        verifyRecord.setVerifyType(CaseFillingEnum.NOT_PUT_ON.name());

        if (ObjectUtil.isNotEmpty(fileCaseDto.getVerifyReason())) {
            String verifyReason = fileCaseDto.getVerifyReason()
                    .stream()
                    .map(VerifyReasonEnum::name)
                    .collect(Collectors.joining(","));
            verifyRecord.setVerifyReason(verifyReason);
        }

        if (ObjectUtil.isNotEmpty(fileCaseDto.getMeasure())) {
            String measure = fileCaseDto.getMeasure()
                    .stream()
                    .map(MeasureEnum::name)
                    .collect(Collectors.joining(","));
            verifyRecord.setMeasure(measure);
        }
        verifyRecord.setVerifier(fileCaseDto.getAuditor());
        verifyRecord.setVerifyProcess(VerifyProcessEnum.FINAL_TRAIL.getDescription());
        verifyRecord.setVerifyLog(fileCaseDto.getAuditOpinion());
        verifyRecord.setVerifyTime(DateUtils.getNowDate());
        verifyRecord.setRelateUnit(fileCaseDto.getRelateUnit());

        proposalVerifyRecordMapper.insert(verifyRecord);
    }

    @Override
    public void addMergeCaseVerifyRecord(MergeCaseDto mergeCaseDto, String serialNumberStr) {

        String template = "同意 审核操作: 将流水号为（{0}）的提案并入";

        ProposalVerifyRecord verifyRecord = new ProposalVerifyRecord();
        verifyRecord.setProposalId(mergeCaseDto.getMainCaseId());
        verifyRecord.setVerifyType(CaseFillingEnum.MERGED.name());
        verifyRecord.setVerifyProcess(VerifyProcessEnum.FINAL_TRAIL.getDescription());
        verifyRecord.setVerifyTime(DateUtils.getNowDate());

        verifyRecord.setVerifyLog(MessageFormat.format(template, serialNumberStr));
        proposalVerifyRecordMapper.insert(verifyRecord);
    }

    @Override
    public void addCancelCaseVerifyRecord(FileCaseDto fileCaseDto) {
        ProposalVerifyRecord verifyRecord = new ProposalVerifyRecord();
        verifyRecord.setProposalId(fileCaseDto.getProposalId());
        verifyRecord.setVerifyType(CaseFillingEnum.WITHDRAW.name());
        verifyRecord.setVerifyProcess(VerifyProcessEnum.FINAL_TRAIL.getDescription());
        verifyRecord.setVerifyLog(fileCaseDto.getAuditOpinion());
        verifyRecord.setVerifyTime(DateUtils.getNowDate());
        verifyRecord.setVerifier(fileCaseDto.getAuditor());

        proposalVerifyRecordMapper.insert(verifyRecord);
    }

    @Override
    public void addAssignCaseVerifyRecord(FileCaseDto fileCaseDto) {
        ProposalVerifyRecord verifyRecord = new ProposalVerifyRecord();
        verifyRecord.setProposalId(fileCaseDto.getProposalId());
        verifyRecord.setVerifyType(CaseFillingEnum.WAIT_HANDLE.name());
        verifyRecord.setVerifyProcess("交办");

        String isOpen = fileCaseDto.getIsOpen() ? "公开" : "不公开";
        String undertakeWay = fileCaseDto.getUndertakeWay().getDescription();
        StringBuilder undertakeUnit =  new StringBuilder();
        if (ObjectUtil.isNotEmpty(fileCaseDto.getUndertakeUnits())) {
            for (UndertakeUnitDto unit : fileCaseDto.getUndertakeUnits()) {
                if (unit.getUndertakeWay().equals(UndertakeWayEnum.LEAD_OFFICE)) {
                    undertakeUnit.insert(0, "【" + unit.getUndertakeWay().getDescription() + "】: " + unit.getUnitName() + " ");
                } else {
                    undertakeUnit.append("【").append(unit.getUndertakeWay().getDescription()).append("】: ").append(unit.getUnitName()).append(" ");
                }
            }
        }

        String verifyLog = MessageFormat.format(VERIFY_LOG_TEMPLATE, fileCaseDto.getAuditOpinion(),
                "", "交办", isOpen, undertakeWay, undertakeUnit.toString());
        verifyRecord.setVerifyLog(verifyLog);
        verifyRecord.setVerifyTime(DateUtils.getNowDate());
        verifyRecord.setVerifier(fileCaseDto.getAuditor());

        proposalVerifyRecordMapper.insert(verifyRecord);
    }

    /**
     * 查询提案审核记录信息
     * 
     * @param id 提案审核记录信息主键
     * @return 提案审核记录信息
     */
    @Override
    public ProposalVerifyRecord selectProposalVerifyRecordById(Long id) {
        return proposalVerifyRecordMapper.selectById(id);
    }

    /**
     * 查询提案审核记录信息列表
     * 
     * @param proposalVerifyRecord 提案审核记录信息
     * @return 提案审核记录信息
     */
    @Override
    public List<ProposalVerifyRecord> selectProposalVerifyRecordList(ProposalVerifyRecord proposalVerifyRecord) {
        return proposalVerifyRecordMapper.selectProposalVerifyRecordList(proposalVerifyRecord);
    }


    @Override
    public List<ProposalVerifyRecordVo> selectProposalVerifyRecordList(Long proposalId) {
        return proposalVerifyRecordMapper.selectProposalVerifyRecordByProposalId(proposalId);
    }
}
