package com.ruoyi.project.proposal.service.impl;

import java.util.List;

import com.ruoyi.project.proposal.domain.vo.ProposalQualityEvaluationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.proposal.mapper.ProposalQualityEvaluationMapper;
import com.ruoyi.project.proposal.domain.ProposalQualityEvaluation;
import com.ruoyi.project.proposal.service.IProposalQualityEvaluationService;

/**
 * 质量评价信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class ProposalQualityEvaluationServiceImpl implements IProposalQualityEvaluationService {
    @Autowired
    private ProposalQualityEvaluationMapper proposalQualityEvaluationMapper;

    /**
     * 查询质量评价信息
     * 
     * @param id 质量评价信息主键
     * @return 质量评价信息
     */
    @Override
    public ProposalQualityEvaluation selectProposalQualityEvaluationById(Long id) {
        return proposalQualityEvaluationMapper.selectById(id);
    }

    /**
     * 查询质量评价信息列表
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 质量评价信息
     */
    @Override
    public List<ProposalQualityEvaluation> selectProposalQualityEvaluationList(ProposalQualityEvaluation proposalQualityEvaluation) {
        return proposalQualityEvaluationMapper.selectProposalQualityEvaluationList(proposalQualityEvaluation);
    }

    /**
     * 新增质量评价信息
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 结果
     */
    @Override
    public int insertProposalQualityEvaluation(ProposalQualityEvaluation proposalQualityEvaluation) {
        return proposalQualityEvaluationMapper.insert(proposalQualityEvaluation);
    }

    /**
     * 修改质量评价信息
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 结果
     */
    @Override
    public int updateProposalQualityEvaluation(ProposalQualityEvaluation proposalQualityEvaluation) {
        return proposalQualityEvaluationMapper.updateById(proposalQualityEvaluation);
    }

    /**
     * 批量删除质量评价信息
     * 
     * @param ids 需要删除的质量评价信息主键
     * @return 结果
     */
    @Override
    public int deleteProposalQualityEvaluationByIds(Long[] ids) {
        return proposalQualityEvaluationMapper.deleteProposalQualityEvaluationByIds(ids);
    }

    /**
     * 删除质量评价信息信息
     * 
     * @param id 质量评价信息主键
     * @return 结果
     */
    @Override
    public int deleteProposalQualityEvaluationById(Long id)
    {
        return proposalQualityEvaluationMapper.deleteById(id);
    }

    @Override
    public List<ProposalQualityEvaluationVo> selectEvaluationList(Long proposalId) {
        return proposalQualityEvaluationMapper.selectEvaluationList(proposalId);
    }
}
