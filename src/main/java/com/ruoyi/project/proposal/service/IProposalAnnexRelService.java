package com.ruoyi.project.proposal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.proposal.domain.ProposalAnnexRel;

import java.util.List;

public interface IProposalAnnexRelService extends IService<ProposalAnnexRel> {

    void insertProposalAnnexRel(Long proposalId, List<Long> annexIdList);

    void updateProposalAnnexRel(Long proposalId, List<Long> annexIdList);

    void deleteProposalAnnexRel(Long proposalId);
}
