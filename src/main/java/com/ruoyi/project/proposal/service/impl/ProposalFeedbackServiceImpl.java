package com.ruoyi.project.proposal.service.impl;

import cn.hutool.core.collection.SpliteratorUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import com.ruoyi.common.enums.proposal.VerifyProcessEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.proposal.converter.FeedbackConverter;
import com.ruoyi.project.proposal.domain.*;
import com.ruoyi.project.proposal.domain.dto.ProposalFeedbackDto;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.domain.vo.ProposalFeedbackUnitVo;
import com.ruoyi.project.proposal.domain.vo.ProposalFeedbackVo;
import com.ruoyi.project.proposal.domain.vo.ProposalReplyFeedbackVo;
import com.ruoyi.project.proposal.mapper.*;
import com.ruoyi.project.proposal.service.IProposalFeedbackService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ProposalFeedbackServiceImpl implements IProposalFeedbackService {

    @Resource
    private ProposalMapper proposalMapper;

    @Resource
    private ProposalFeedbackMapper feedbackMapper;

    @Resource
    private ProposalHandleMapper handleMapper;

    @Resource
    private ProposalUndertakeUnitMapper undertakeUnitMapper;

    @Resource
    private ProposalFeedbackAnnexMapper feedbackAnnexMapper;

    @Resource
    private ProposalVerifyRecordMapper verifyRecordMapper;

    @Resource
    private ProposalReceptionMapper receptionMapper;

    @Override
    public ProposalReplyFeedbackVo selectProposalFeedbackVoById(String proposalId) {
        ProposalReplyFeedbackVo replyFeedbackVo = new ProposalReplyFeedbackVo();

        List<ProposalFeedbackVo> feedbackList = feedbackMapper.selectProposalFeedbackList(proposalId);

        if (ObjectUtil.isNotEmpty(feedbackList)) {
            for (ProposalFeedbackVo item : feedbackList) {

                List<AnnexVo> annexVoList = feedbackAnnexMapper.selectFeedbackAnnexList(item.getId());
                item.setReplyAnnexList(annexVoList);

                String proposer = item.getProposer().replace("(领衔)", "")
                        .replace("(附议)", "")
                        .replace("(单独)", "")
                        .replace(",", "、");
                item.setProposer(proposer);

                if (item.getUndertakeWay().equals(UndertakeWayEnum.LEAD_OFFICE) || item.getUndertakeWay().equals(UndertakeWayEnum.SINGLE_OFFICE)) {
                    replyFeedbackVo.setLeadFeedback(item);
                } else if (item.getUndertakeWay().equals(UndertakeWayEnum.ASSISTANT_OFFICE) || item.getUndertakeWay().equals(UndertakeWayEnum.DISTRIBUTED_WORK)) {
                    replyFeedbackVo.getCoFeedbackList().add(item);
                }
            }
        }

        return replyFeedbackVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertProposalFeedback(ProposalFeedbackDto feedbackDto) {

        // 承办方式判断
        ProposalUndertakeUnit undertakeUnit = undertakeUnitMapper.selectUndertakeUnitByProposalId(feedbackDto.getProposalId(), SecurityUtils.getUserId());

        // 插入承办反馈信息
        ProposalFeedback feedback = FeedbackConverter.INSTANCE.convert(feedbackDto);
        feedback.setReplyUserId(String.valueOf(undertakeUnit.getUnitId()));
        feedback.setUndertakeWay(undertakeUnit.getUndertakeWay());
        feedbackMapper.insert(feedback);

        // 插入附件信息
        if (ObjectUtil.isNotEmpty(feedbackDto.getAnnexIdList())) {
            for (String id : feedbackDto.getAnnexIdList()) {
                ProposalFeedbackAnnex feedbackAnnex = new ProposalFeedbackAnnex();
                feedbackAnnex.setFeedbackId(feedback.getId());
                feedbackAnnex.setAnnexId(id);
                feedbackAnnexMapper.insert(feedbackAnnex);
            }
        }
        // 判断提案是否办结
        ProposalHandle proposalHandle = handleMapper.getProposalHandleByProposalId(feedbackDto.getProposalId());
        if (ObjectUtil.isNotEmpty(proposalHandle)) {

            ProposalHandle updateObj = new ProposalHandle();

            // 并案信息
//            ProposalMerge mergeInfo = proposalMapper.getMergeInfo(feedbackDto.getProposalId());
            switch (UndertakeWayEnum.valueOf(proposalHandle.getUndertakeWay())) {
                // 单办
                case SINGLE_OFFICE:
                    proposalMapper.changeCaseFiling(feedbackDto.getProposalId(), CaseFillingEnum.FINISH);
                    setUpdateHandleInfo(updateObj, feedbackDto);
//                    handleMergeProposal(mergeInfo);
                    break;
                // 会办
                case JOINT_HANDLING:
                    if (undertakeUnit.getUndertakeWay().equals(UndertakeWayEnum.LEAD_OFFICE)) {
                        proposalMapper.changeCaseFiling(feedbackDto.getProposalId(), CaseFillingEnum.FINISH);
                        setUpdateHandleInfo(updateObj, feedbackDto);
//                        handleMergeProposal(mergeInfo);
                    }
                    break;
                // 分办
                case DISTRIBUTED_WORK:
                    List<ProposalUndertakeUnit> unitList = undertakeUnitMapper.selectUndertakeUnitList(feedbackDto.getProposalId());
                    if (ObjectUtil.isNotEmpty(unitList)) {
                        boolean flag = unitList.stream()
                                .filter(item -> !Objects.equals(item.getUnitId(), SecurityUtils.getUserId()))
                                .allMatch(ProposalUndertakeUnit::getHandleStatus);
                        if (flag) {
                            proposalMapper.changeCaseFiling(feedbackDto.getProposalId(), CaseFillingEnum.FINISH);
                            setUpdateHandleInfo(updateObj, feedbackDto);
//                            handleMergeProposal(mergeInfo);
                        }
                    }
                    break;
                default:
                    break;
            }

            handleMapper.update(updateObj, new LambdaUpdateWrapper<ProposalHandle>()
                    .eq(ProposalHandle::getId, proposalHandle.getId())
                    .setSql("handle_count = handle_count + 1")
            );

        }

        undertakeUnitMapper.update(null, new LambdaUpdateWrapper<ProposalUndertakeUnit>()
                .set(ProposalUndertakeUnit::getHandleStatus, true)
                .set(ProposalUndertakeUnit::getUndertakeResult, feedbackDto.getUndertakeResult())
                .set(ProposalUndertakeUnit::getHandleWay, feedbackDto.getHandleWay())
                .eq(ProposalUndertakeUnit::getId, undertakeUnit.getId())
        );

        // 提案审核信息
        ProposalVerifyRecord verifyRecord = new ProposalVerifyRecord();
        verifyRecord.setProposalId(feedbackDto.getProposalId());
        verifyRecord.setVerifier(SecurityUtils.getUsername());
        verifyRecord.setVerifyProcess(VerifyProcessEnum.HANDLE.getDescription());
        verifyRecord.setVerifyTime(new Date());
        verifyRecordMapper.insert(verifyRecord);

        return feedback.getId();
    }

    // 设置更新办理信息
    private void setUpdateHandleInfo(ProposalHandle updateObj, ProposalFeedbackDto feedbackDto) {
        updateObj.setUndertakePerson(feedbackDto.getUndertakePerson());
        updateObj.setUndertakeResult(feedbackDto.getUndertakeResult().name());
        updateObj.setProposalQuality(feedbackDto.getProposalQuality().name());
        updateObj.setHandleWay(feedbackDto.getHandleWay().name());
        updateObj.setUndertakeTime(feedbackDto.getUndertakeTime());
    }

    // 处理合并提案
    private void handleMergeProposal(ProposalMerge mergeInfo) {
        if (ObjectUtil.isNotEmpty(mergeInfo)) {
            List<String> idList = Arrays.asList(mergeInfo.getMergeProposal().split(","));
            proposalMapper.mergeHandle(idList);
        }
    }

    @Override
    public ProposalFeedbackUnitVo getUnitFeedback(String proposalId) {
        Long unitId = SecurityUtils.getUserId();
        ProposalFeedback unitFeedback =  feedbackMapper.selectUnitFeedbackById(proposalId, unitId);
        ProposalFeedbackUnitVo feedbackVo;
        if (ObjectUtil.isNotEmpty(unitFeedback)) {
            feedbackVo = FeedbackConverter.INSTANCE.convert(unitFeedback);
            feedbackVo.setAnnexList(feedbackMapper.selectProposalFeedbackAnnexList(feedbackVo.getId()));
            feedbackVo.setCanReply(false);

        } else {
            feedbackVo = new ProposalFeedbackUnitVo();
            feedbackVo.setCanReply(checkCanReply(proposalId, unitId));
        }

        return feedbackVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProposalFeedbackAnnex(ProposalFeedbackDto feedbackDto) {

        ProposalFeedback proposalFeedback = feedbackMapper.selectById(feedbackDto.getId());
        if (proposalFeedback == null) {
            throw new ServiceException("未查询到该提案！");
        }

        feedbackAnnexMapper.deletedFeedbackAnnex(feedbackDto.getId());
        if (ObjectUtil.isNotEmpty(feedbackDto.getAnnexIdList())) {
            for (String id : feedbackDto.getAnnexIdList()) {
                ProposalFeedbackAnnex feedbackAnnex = new ProposalFeedbackAnnex();
                feedbackAnnex.setFeedbackId(feedbackDto.getId());
                feedbackAnnex.setAnnexId(id);
                feedbackAnnexMapper.insert(feedbackAnnex);
            }
        }

        return true;
    }

    private Boolean checkCanReply(String proposalId, Long unitID) {

        if (!receptionMapper.checkReceived(proposalId, unitID)) {
//            throw new ServiceException("未接收提案！请先接收提案再进行答复！");
            return false;
        }


        ProposalHandle proposalHandle = handleMapper.getProposalHandleByProposalId(proposalId);
        if (ObjectUtil.isNotEmpty(proposalHandle)) {

            if (proposalHandle.getUndertakeWay().equals(UndertakeWayEnum.SINGLE_OFFICE.name())) {
                // 单办
                return true;
            } else if (proposalHandle.getUndertakeWay().equals(UndertakeWayEnum.JOINT_HANDLING.name())) {
                // 会办
                List<ProposalUndertakeUnit> unitList = undertakeUnitMapper.selectUndertakeUnitList(proposalId);
                if (ObjectUtil.isNotEmpty(unitList)) {
                    ProposalUndertakeUnit leadUnit = unitList.stream()
                            .filter(item -> item.getUndertakeWay().equals(UndertakeWayEnum.LEAD_OFFICE))
                            .findFirst()
                            .orElse(new ProposalUndertakeUnit());

                    // 主办单位
                    if (leadUnit.getUnitId().equals(unitID)) {
                        return unitList.stream().filter(item -> item.getUndertakeWay().equals(UndertakeWayEnum.ASSISTANT_OFFICE))
                                .allMatch(item -> Boolean.TRUE.equals(item.getHandleStatus()));
                    } else {
                        // 协办单位
                        return leadUnit.getHandleStatus() != null && !leadUnit.getHandleStatus();
                    }

                }
            } else if (proposalHandle.getUndertakeWay().equals(UndertakeWayEnum.DISTRIBUTED_WORK.name())) {
                // 分办
                return true;
            }
        }

        return false;
    }
}
