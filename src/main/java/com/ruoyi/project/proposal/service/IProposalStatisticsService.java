package com.ruoyi.project.proposal.service;

import com.ruoyi.project.proposal.domain.dto.ProposalStatisticsDto;
import com.ruoyi.project.proposal.domain.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 提案统计服务接口
 */
public interface IProposalStatisticsService {

    /**
     * 办理结果统计
     * @param statisticsDto 统计参数
     * @return 统计结果
     */
    List<HandleStatisticsVo> analyzeConsultResult(ProposalStatisticsDto statisticsDto);

    /**
     * 单位件数统计
     * @param statisticsDto 统计参数
     * @return 统计结果
     */
    List<ProposalUnitStatisticVo> analyzeDeptProposalCount(ProposalStatisticsDto statisticsDto);

    /**
     * 单位件数统计导出
     * @param statisticsDto 统计参数
     * @param response 响应对象
     */
    void exportDeptProposal(ProposalStatisticsDto statisticsDto, HttpServletResponse response);

    /**
     * 办理件数统计
     * @param statisticsDto 统计参数
     * @return 统计结果
     */
    List<HandleCaseCountVo> analyzeByGovOrPartyDept(ProposalStatisticsDto statisticsDto);

    /**
     * 办理进度统计
     * @param statisticsDto 统计参数
     * @return 统计结果
     */
    List<HandleProgressStatsVo> analyzeHandleProgress(ProposalStatisticsDto statisticsDto);

    /**
     * 接收情况统计
     * @param statisticsDto 统计参数
     * @return 统计结果
     */
    List<ProposalReceptionStatisticsVo> analyzeProposalReception(ProposalStatisticsDto statisticsDto);

    /**
     * 办理情况通报
     * @param statisticsDto 统计参数
     * @return 统计结果
     */
    Object analyzeHandleNotice(ProposalStatisticsDto statisticsDto);

    /**
     * 委员提案统计
     * @param year 年份
     * @return 统计结果
     */
    List<MemberProposalStatisticsVo> analyzeMemberProposal(Integer year);

    /**
     * 委员提案统计导出
     * @param year 年份
     * @param response 响应对象
     */
    void exportMemberProposal(Integer year, HttpServletResponse response);

    /**
     * 委员立案详细统计
     * @param year 年份
     * @return 统计结果
     */
    List<MemberProposalDetailStatisticsVo> analyzeMemberProposalDetail(Integer year);

    /**
     * 委员立案详细统计导出
     * @param year 年份
     * @param response 响应对象
     */
    void exportMemberProposalDetail(Integer year, HttpServletResponse response);
}
