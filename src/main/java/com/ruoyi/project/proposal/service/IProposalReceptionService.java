package com.ruoyi.project.proposal.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.project.proposal.domain.ProposalReception;
import com.ruoyi.project.proposal.domain.dto.UndertakeUnitDto;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionEditVo;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionVo;

/**
 * 提案接收情况信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IProposalReceptionService {
    /**
     * 查询提案接收情况信息
     * 
     * @param id 提案接收情况信息主键
     * @return 提案接收情况信息
     */
    public ProposalReception selectProposalReceptionById(Long id);

    /**
     * 查询提案接收情况信息列表
     *
     * @param proposalId 提案id
     * @return 提案接收情况信息集合
     */
    public List<ProposalReceptionVo> selectProposalReceptionList(String proposalId);

    /**
     * 新增提案接收情况信息
     * 
     * @param receptionEditVo 提案接收情况信息
     * @return 结果
     */
    public int insertProposalReception(ProposalReceptionEditVo receptionEditVo);

    /**
     * 修改提案接收情况信息
     * 
     * @param receptionEditVo 提案接收情况信息
     * @return 结果
     */
    public int updateProposalReception(ProposalReceptionEditVo receptionEditVo);

    /**
     * 批量删除提案接收情况信息
     * 
     * @param ids 需要删除的提案接收情况信息主键集合
     * @return 结果
     */
    public int deleteProposalReceptionByIds(Long[] ids);

    /**
     * 删除提案接收情况信息信息
     * 
     * @param id 提案接收情况信息主键
     * @return 结果
     */
    public int deleteProposalReceptionById(Long id);

    /**
     * 新增接收情况
     *
     * @param proposalId proposalId
     * @param undertakeUnits undertakeUnits
     */
    public void addReception(String proposalId, List<UndertakeUnitDto> undertakeUnits);

    Boolean receive(String proposalId);

    Map<String, List<ProposalReceptionVo>> selectReceptionByIds(List<String> idList);

    /**
     * 保存或更新接收情况
     * @param proposalId proposalId
     * @param undertakeUnits undertakeUnits
     */
    public void saveOrUpdate(String proposalId, List<UndertakeUnitDto> undertakeUnits);
}
