package com.ruoyi.project.proposal.service.impl;

import com.ruoyi.project.proposal.domain.ProposalMerge;
import com.ruoyi.project.proposal.domain.dto.MergeCaseDetailDto;
import com.ruoyi.project.proposal.domain.dto.MergeCaseDto;
import com.ruoyi.project.proposal.mapper.ProposalMergeMapper;
import com.ruoyi.project.proposal.service.IProposalMergeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

@Service
public class ProposalMergeServiceImpl implements IProposalMergeService {

    @Resource
    private ProposalMergeMapper proposalMergeMapper;

    @Override
    public void addMergeInfo(MergeCaseDto mergeCaseDto) {
        String mergeCaseId = mergeCaseDto.getMergeCaseList().stream()
                .map(MergeCaseDetailDto::getCaseId)
                .collect(Collectors.joining(","));
        ProposalMerge proposalMerge = new ProposalMerge();
        proposalMerge.setMainProposal(mergeCaseDto.getMainCaseId());
        proposalMerge.setMergeProposal(mergeCaseId);
        proposalMergeMapper.insert(proposalMerge);
    }
}
