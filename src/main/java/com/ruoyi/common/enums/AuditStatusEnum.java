package com.ruoyi.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 审核状态
 * @Param
 * @return
 **/
@Getter
public enum AuditStatusEnum implements IEnum<String> {
    UNREVIEWED("0", "待审核"),
    REVIEWED("1", "已审核"),
    RETURNED("2", "已退回");

    @EnumValue
    private final String code;
    private final String label;

    // 静态映射，用于快速查找
    private static final Map<String, AuditStatusEnum> CODE_MAP = new HashMap<>();
    private static final Map<String, AuditStatusEnum> LABEL_MAP = new HashMap<>();

    static {
        for (AuditStatusEnum status : AuditStatusEnum.values()) {
            CODE_MAP.put(status.code, status);
            LABEL_MAP.put(status.label, status);
        }
    }

    AuditStatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    @Override
    public String getValue() {
        return this.code;
    }

    /**
     * 根据值获取枚举实例
     * @param code 枚举值
     * @return 枚举实例
     */
    public static AuditStatusEnum fromValue(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据标签获取枚举实例
     * @param label 标签名称（如"已审核"）
     * @return 枚举实例
     */
    public static AuditStatusEnum fromLabel(String label) {
        return LABEL_MAP.get(label);
    }

    /**
     * 兼容整数类型的查询
     */
    public static AuditStatusEnum fromValue(int code) {
        return fromValue(String.valueOf(code));
    }


}