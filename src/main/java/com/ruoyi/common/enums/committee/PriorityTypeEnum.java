package com.ruoyi.common.enums.committee;

import lombok.Getter;

@Getter
public enum PriorityTypeEnum {

    ECO_MONITOR("1", "参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动"),
    RURAL_DEVELOP("2", "参加脱贫攻坚与乡村振兴工作"),
    GRASSROOTS_NEG("3", "参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理"),
    READING_PROMOTION("4", "参加书香政协学习、宣讲活动"),
    EPIDEMIC_PREVENT("5", "参加新冠肺炎疫情防控"),
    PROJECT_ASSIST("6", "助力项目建设服务，参加引进外资活动"),
    BUSINESS_ENV("7", "助推创一流营商环境"),
    OTHER_TASK("8", "完成区政协交办的其他任务");

    private final String code;
    private final String desc;

    PriorityTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    /**
     * 根据编码获取枚举对象
     */
    public static PriorityTypeEnum fromCode(String code) {
        for (PriorityTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
