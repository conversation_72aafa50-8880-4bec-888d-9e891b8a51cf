package com.ruoyi.common.enums.committee;

import lombok.Getter;

@Getter
public enum SignTypeEnum {

    PC("1", "电脑端签到"),
    SELF("2", "本人"),
    PROXY("3", "工作人员代签"),
    UNSIGNED("4", "未签到");

    private final String code;
    private final String description;

    SignTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举对象
     */
    public static SignTypeEnum getDescriptionByCode(String code) {
        for (SignTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
