package com.ruoyi.common.enums.committee;

import lombok.Getter;

@Getter
public enum PersonTypeEnum {
    WRITER("WRITER", "执笔者"),
    PARTICIPANT("PARTICIPANT", "参与者"),
    GOOD("GOOD", "表现较好"),
    OUTSTANDING("OUTSTANDING", "特别突出");

//

    private final String code;
    private final String description;

    PersonTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }


    /**
     * 根据 code 获取枚举对象
     */
    public static PersonTypeEnum fromCode(String code) {
        for (PersonTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
