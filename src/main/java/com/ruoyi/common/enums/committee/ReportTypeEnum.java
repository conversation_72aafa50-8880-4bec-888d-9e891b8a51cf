package com.ruoyi.common.enums.committee;

import lombok.Getter;

@Getter
public enum ReportTypeEnum {

    RESEARCH("RESEARCH", "调研报告"),
    ARTICLE("ARTICLE", "发表文章"),
    KEY_WORK("KEY_WORK", "年度重点工作"),
    AWARD("AWARD", "获奖情况"),
    WELFARE("WELFARE", "公益情况");



    private final String code;
    private final String description;

    ReportTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }


    /**
     * 根据 code 获取枚举对象
     */
    public static ReportTypeEnum fromCode(String code) {
        for (ReportTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
