package com.ruoyi.common.enums;

import lombok.Getter;

@Getter
public enum AdminKey {

    ADMIN("admin"),

    TIANYA("super")

    ;

    private final String key;

    AdminKey(String key) {
        this.key = key;
    }

    public static boolean contains(String key) {
        for (AdminKey adminKey : AdminKey.values()) {
            if (adminKey.getKey().equals(key)) {
                return true;
            }
        }
        return false;
    }
}
