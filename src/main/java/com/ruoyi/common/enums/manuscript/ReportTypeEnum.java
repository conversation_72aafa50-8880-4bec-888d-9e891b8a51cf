package com.ruoyi.common.enums.manuscript;

import lombok.Getter;

/**
 * 报道类型枚举
 */
@Getter
public enum ReportTypeEnum {
    /**
     * 要情专报
     * 专报
     * 社情民意
     * 调研报告
     * 政协工作交流
     * 业务通讯
     */
    INFORMATION_REPORT("要情专报"),
    SPECIAL_REPORT("专报"),
    SOCIAL_OPINION("社情民意"),
    RESEARCH_REPORT("调研报告"),
    PARLIAMENT_WORK_INTERCHANGE("政协工作交流"),
    BUSINESS_COMMUNICATION("业务通讯");

    private final String description;

    ReportTypeEnum(String description) {
        this.description = description;
    }
}
