package com.ruoyi.common.enums.manuscript;

import lombok.Getter;

/**
 * 稿件类型枚举
 */
@Getter
public enum CategoryEnum {
    /**
     * Economic     经济
     * Political    政治
     * Cultural     文化
     * Social       社会
     * Eco-civilization 生态文明
     * Other        其他
     */
    ECONOMIC("经济"),
    POLITICAL("政治"),
    CULTURAL("文化"),
    SOCIAL("社会"),
    ECO_CIVILIZATION("生态文明"),
    OTHER("其他");

    private final String description;

    CategoryEnum(String description) {
        this.description = description;
    }

    public static CategoryEnum getByName(String enumName) {
        if (enumName == null) {
            return null;
        }

        for (CategoryEnum value : values()) {
            if (value.name().equals(enumName)) {
                return value;
            }
        }
        return null;
    }
}
