package com.ruoyi.common.enums.manuscript;

import lombok.Getter;

/**
 * 稿件状态枚举
 */
public enum ManuscriptStatusEnum implements IEnum {
    /**
     * 投稿暂存
     * 待审核
     * 不采纳
     * 素材
     * 待签发
     * 已签发
     * 已办结
     * 退回修改
     * PS: 已采用和未采用仅在查询是使用，不录入数据库
     */
    DRAFT("投稿暂存"),
    AUDITING("待审核"),
    DISCARD("不采纳"),
    MATERIAL("素材"),
    WAIT_FOR_ISSUE("待签发"),
    ISSUED("已签发"),
    FINISHED("已办结"),
    RETURN_MODIFY("退回修改"),
    ADOPTED("已采用"),
    NOT_ADOPTED("未采用");

    private final String description;

    ManuscriptStatusEnum(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
