package com.ruoyi.common.enums.manuscript;


import lombok.Getter;

import java.io.Serializable;

/**
 * 采用方式枚举
 */
public enum AdoptWayEnum {
    /**
     * 不采用
     * 单篇采用
     * 综合采用
     */
    NOT_ADOPT("不采用"),
    SINGLE_ADOPT("单篇采用"),
    COMPREHENSIVE_ADOPT("综合采用");


    private final String description;

    AdoptWayEnum(String description) {
        this.description = description;
    }

}
