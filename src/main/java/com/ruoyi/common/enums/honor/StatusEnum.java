package com.ruoyi.common.enums.honor;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 状态枚举
 * @Param
 * @return
 **/
@Getter
public enum StatusEnum implements IEnum<String> {
    DRAFT("0", "暂存"),
    SUBMITTED("1", "提交");

    @EnumValue
    private final String code;
    private final String label;

    // 静态映射，用于快速查找
    private static final Map<String, StatusEnum> CODE_MAP = new HashMap<>();
    private static final Map<String, StatusEnum> LABEL_MAP = new HashMap<>();

    static {
        for (StatusEnum status : StatusEnum.values()) {
            CODE_MAP.put(status.code, status);
            LABEL_MAP.put(status.label, status);
        }
    }

    StatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    @Override
    public String getValue() {
        return this.code;
    }

    /**
     * 根据值获取枚举实例
     * @param code 枚举值
     * @return 枚举实例
     */
    public static StatusEnum fromValue(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 兼容整数类型的查询
     */
    public static StatusEnum fromValue(int code) {
        return fromValue(String.valueOf(code));
    }

    /**
     * 根据标签获取枚举实例
     * @param label 标签名称（如"暂存"）
     * @return 枚举实例
     */
    public static StatusEnum fromLabel(String label) {
        return LABEL_MAP.get(label);
    }
}
