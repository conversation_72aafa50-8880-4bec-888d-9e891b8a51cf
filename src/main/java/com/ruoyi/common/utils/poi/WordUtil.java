package com.ruoyi.common.utils.poi;

import com.ruoyi.common.exception.ServiceException;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

public class WordUtil {

    private static final Logger log = LoggerFactory.getLogger(WordUtil.class);

    public static void write(String fileName, XWPFDocument document, HttpServletResponse response) {
        try {
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            ServletOutputStream out = response.getOutputStream();
            document.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            log.error("导出Word失败 >> {}", e.getMessage());
            throw new ServiceException("导出Word失败");
        }
    }
}
