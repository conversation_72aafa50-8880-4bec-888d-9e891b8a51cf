package com.ruoyi.common.utils.collections;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.function.BiConsumer;

/**
 * A Map implementation that associates one key with two values
 * @param <K> Type of the key
 * @param <V1> Type of the first value
 * @param <V2> Type of the second value
 */
public class TripleMap<K, V1, V2> {
    private final Map<K, Pair<V1, V2>> map;
    private final boolean nullAllowed;

    /**
     * Constructor (allows null values by default)
     */
    public TripleMap() {
        this(new HashMap<>(), true);
    }

    /**
     * Constructor
     * @param nullAllowed Whether null values are permitted
     */
    public TripleMap(boolean nullAllowed) {
        this(new HashMap<>(), nullAllowed);
    }

    /**
     * Constructor based on existing map
     * @param backingMap The underlying map to use
     * @param nullAllowed Whether null values are permitted
     * @throws NullPointerException if backingMap is null
     */
    public TripleMap(Map<K, Pair<V1, V2>> backingMap, boolean nullAllowed) {
        this.map = Objects.requireNonNull(backingMap, "Backing map cannot be null");
        this.nullAllowed = nullAllowed;
    }

    /**
     * Associates the specified values with the specified key
     * @param key The key
     * @param value1 The first value
     * @param value2 The second value
     * @throws NullPointerException if nulls aren't allowed and any parameter is null
     */
    public void put(K key, V1 value1, V2 value2) {
        checkNull(key, value1, value2);
        map.put(key, ImmutablePair.of(value1, value2));
    }

    /**
     * Returns the value pair associated with the key
     * @return An immutable Pair, or null if key doesn't exist
     */
    public Pair<V1, V2> get(K key) {
        return map.get(key);
    }

    /**
     * Gets the first value associated with the key
     * @return The first value, or null if key doesn't exist
     */
    public V1 getFirstValue(K key) {
        Pair<V1, V2> pair = map.get(key);
        return pair != null ? pair.getLeft() : null;
    }

    /**
     * Gets the second value associated with the key
     * @return The second value, or null if key doesn't exist
     */
    public V2 getSecondValue(K key) {
        Pair<V1, V2> pair = map.get(key);
        return pair != null ? pair.getRight() : null;
    }

    /**
     * Checks if the map contains the specified key
     */
    public boolean containsKey(K key) {
        return map.containsKey(key);
    }

    /**
     * Checks if the map contains the specified value pair
     */
    public boolean containsValue(V1 value1, V2 value2) {
        return map.containsValue(ImmutablePair.of(value1, value2));
    }

    /**
     * Removes the mapping for the specified key
     * @return The removed value pair, or null if key didn't exist
     */
    public Pair<V1, V2> remove(K key) {
        return map.remove(key);
    }

    /**
     * Removes all mappings
     */
    public void clear() {
        map.clear();
    }

    /**
     * Returns a Set view of the keys
     * @return An unmodifiable set of keys
     */
    public Set<K> keySet() {
        return Collections.unmodifiableSet(map.keySet());
    }

    /**
     * Returns a Collection view of the value pairs
     * @return An unmodifiable collection of value pairs
     */
    public Collection<Pair<V1, V2>> values() {
        return Collections.unmodifiableCollection(map.values());
    }

    /**
     * Returns a Set view of the mappings
     * @return An unmodifiable set of entries
     */
    public Set<Map.Entry<K, Pair<V1, V2>>> entrySet() {
        return Collections.unmodifiableSet(map.entrySet());
    }

    /**
     * Performs the given action for each mapping
     * @param action The action to perform
     * @throws NullPointerException if action is null
     */
    public void forEach(BiConsumer<? super K, ? super Pair<V1, V2>> action) {
        Objects.requireNonNull(action);
        map.forEach(action);
    }

    /**
     * Returns the number of mappings
     */
    public int size() {
        return map.size();
    }

    /**
     * Checks if the map is empty
     */
    public boolean isEmpty() {
        return map.isEmpty();
    }

    /**
     * Creates a defensive copy
     * @return A new TripleMap containing the same mappings
     */
    public TripleMap<K, V1, V2> copy() {
        return new TripleMap<>(new HashMap<>(map), nullAllowed);
    }

    /**
     * Converts to a standard Map
     * @return A new HashMap containing the same mappings
     */
    public Map<K, Pair<V1, V2>> toMap() {
        return new HashMap<>(map);
    }

    // Private helper method
    private void checkNull(K key, V1 value1, V2 value2) {
        if (!nullAllowed) {
            Objects.requireNonNull(key, "Key cannot be null");
            Objects.requireNonNull(value1, "First value cannot be null");
            Objects.requireNonNull(value2, "Second value cannot be null");
        }
    }

    // Static factory methods

    /**
     * Creates an empty TripleMap
     * @return A new empty TripleMap
     */
    public static <K, V1, V2> TripleMap<K, V1, V2> create() {
        return new TripleMap<>();
    }

    /**
     * Creates a TripleMap from an existing map
     * @param source The source map to copy
     * @return A new TripleMap containing the same mappings
     */
    public static <K, V1, V2> TripleMap<K, V1, V2> createFromMap(
            Map<? extends K, ? extends Pair<V1, V2>> source) {
        TripleMap<K, V1, V2> tripleMap = new TripleMap<>();
        tripleMap.map.putAll(source);
        return tripleMap;
    }
}