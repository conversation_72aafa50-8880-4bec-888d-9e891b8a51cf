name: 🚨部署到正式环境
on:
  workflow_dispatch:
  
jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        ref: main
      
    - name: Set up JDK
      uses: actions/setup-java@v3
      with:
        java-version: '8'
        distribution: 'corretto'
        
    - name: Build with <PERSON><PERSON>
      run: mvn clean package -DskipTests
      
    - name: Upload JAR
      run: |
        cp target/tianya.jar target/file.zip
        curl -X POST \
          -H "Authorization: ${{ secrets.UPLOAD_TOKEN }}" \
          -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
          -H "Host: ${{ secrets.UPLOAD_HOST }}" \
          -F "file=@target/file.zip" \
          ${{ secrets.UPLOAD_URL }}
