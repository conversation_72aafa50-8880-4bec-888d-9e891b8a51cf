name: Build Docker Image and Upload

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    -
      name: Login docker registry
      uses: docker/login-action@v3
      with:
        registry: registry.cn-hangzhou.aliyuncs.com
        username: ${{ secrets.DOCKER_REGISTRY_USERNAME }}
        password: ${{ secrets.DOCKER_REGISTRY_PASSWORD }}
    -
      name: Build and push
      uses: docker/build-push-action@v6
      with:
        push: true
        tags: registry.cn-hangzhou.aliyuncs.com/toosean/tianya-java:latest
